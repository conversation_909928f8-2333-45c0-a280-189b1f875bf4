import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { NzResultModule } from 'ng-zorro-antd/result';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzTagModule } from 'ng-zorro-antd/tag';

/**
 * 组件加载错误显示组件
 * 
 * 当动态加载组件失败时显示此错误页面
 * 提供错误信息和解决建议
 */
@Component({
  selector: 'app-component-error',
  standalone: true,
  imports: [
    CommonModule,
    NzResultModule,
    NzButtonModule,
    NzIconModule,
    NzCardModule,
    NzAlertModule,
    NzTagModule
  ],
  template: `
    <div class="component-error-container">
      <nz-result
        nzStatus="error"
        nzTitle="组件加载失败"
        nzSubTitle="无法加载指定的页面组件，请检查配置或联系管理员">
        
        <div nz-result-content>
          <!-- 错误信息 -->
          <nz-alert
            nzType="error"
            nzShowIcon
            [nzMessage]="errorMessage"
            [nzDescription]="errorDescription"
            class="error-alert">
          </nz-alert>

          <!-- 路由信息 -->
          <nz-card nzTitle="路由信息" class="route-info-card">
            <div class="info-grid">
              <div class="info-item">
                <label>路由路径:</label>
                <code>{{ routePath }}</code>
              </div>
              <div class="info-item">
                <label>组件名称:</label>
                <code>{{ componentName || '未指定' }}</code>
              </div>
              <div class="info-item">
                <label>页面标题:</label>
                <span>{{ routeTitle || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>错误时间:</label>
                <span>{{ errorTime }}</span>
              </div>
            </div>
          </nz-card>

          <!-- 解决方案 -->
          <nz-card nzTitle="解决方案" class="solution-card">
            <div class="solution-content">
              <h4>🔧 可能的解决方法：</h4>
              <ol>
                <li>
                  <strong>检查组件文件是否存在</strong>
                  <br>确认文件路径：<code>{{ suggestedPath }}</code>
                </li>
                <li>
                  <strong>检查组件导出</strong>
                  <br>确保组件正确导出：<code>export class {{ suggestedComponentName }}</code>
                </li>
                <li>
                  <strong>检查组件映射配置</strong>
                  <br>在 <code>component-mapping.ts</code> 中添加组件映射
                </li>
                <li>
                  <strong>检查网络连接</strong>
                  <br>如果是远程组件，请检查网络连接和服务器状态
                </li>
                <li>
                  <strong>查看控制台错误</strong>
                  <br>打开浏览器开发者工具查看详细错误信息
                </li>
              </ol>

              <h4>📝 组件模板示例：</h4>
              <pre class="code-template">{{ componentTemplate }}</pre>
            </div>
          </nz-card>

          <!-- 技术支持 -->
          <nz-card nzTitle="技术支持" class="support-card">
            <div class="support-content">
              <p>如果问题持续存在，请联系技术支持团队：</p>
              <ul>
                <li>📧 邮箱：support&#64;qq.com</li>
                <li>📞 电话：400-123-4567</li>
                <li>💬 在线客服：点击右下角客服图标</li>
              </ul>
              
              <nz-alert
                nzType="info"
                nzShowIcon
                nzMessage="调试提示"
                nzDescription="请将此页面截图和控制台错误信息一并发送给技术支持，以便快速定位问题。">
              </nz-alert>
            </div>
          </nz-card>
        </div>

        <div nz-result-extra>
          <button nz-button nzType="primary" (click)="goBack()">
            <nz-icon nzType="arrow-left"></nz-icon>
            返回上一页
          </button>
          <button nz-button (click)="reloadPage()">
            <nz-icon nzType="reload"></nz-icon>
            重新加载
          </button>
          <button nz-button nzType="dashed" (click)="reportError()">
            <nz-icon nzType="bug"></nz-icon>
            报告错误
          </button>
        </div>
      </nz-result>
    </div>
  `,
  styles: [`
    .component-error-container {
      padding: 24px;
      min-height: 500px;
    }

    .error-alert {
      margin-bottom: 16px;
    }

    .route-info-card,
    .solution-card,
    .support-card {
      margin-bottom: 16px;
      text-align: left;
    }

    .info-grid {
      display: grid;
      gap: 12px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    .info-item label {
      min-width: 80px;
      font-weight: 600;
      color: #262626;
      flex-shrink: 0;
    }

    .info-item code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }

    .solution-content h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .solution-content ol {
      margin: 8px 0 16px 20px;
      color: #595959;
    }

    .solution-content li {
      margin: 8px 0;
    }

    .solution-content li strong {
      color: #262626;
    }

    .code-template {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 12px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      white-space: pre;
    }

    .support-content p {
      color: #595959;
      margin: 8px 0;
    }

    .support-content ul {
      margin: 8px 0 16px 20px;
      color: #595959;
    }

    .support-content li {
      margin: 4px 0;
    }
  `]
})
export class ComponentErrorComponent {
  private readonly route = inject(ActivatedRoute);

  // 错误信息
  errorMessage = '组件加载失败';
  errorDescription = '系统无法加载指定的组件，可能是文件不存在、网络错误或配置问题。';
  errorTime = new Date().toLocaleString();

  // 从路由数据中获取信息
  get routeData() {
    return this.route.snapshot.data;
  }

  get routePath() {
    return this.route.snapshot.routeConfig?.path || '';
  }

  get componentName() {
    return this.routeData['componentName'] || this.routeData['component'];
  }

  get routeTitle() {
    return this.routeData['title'];
  }

  get suggestedPath() {
    const path = this.routePath;
    const componentName = this.componentName || `${this.kebabCase(path)}.component`;
    return `src/app/pages/dynamic/${path}/${componentName}.ts`;
  }

  get suggestedComponentName() {
    const path = this.routePath;
    return this.componentName || `${this.pascalCase(path)}Component`;
  }

  get componentTemplate() {
    const componentName = this.suggestedComponentName;
    const selector = this.kebabCase(this.routePath);

    return `import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-${selector}',
  standalone: true,
  imports: [CommonModule],
  template: \`
    <div class="${selector}-container">
      <h1>${this.routeTitle || '页面标题'}</h1>
      <p>页面内容...</p>
    </div>
  \`,
  styles: [\`
    .${selector}-container {
      padding: 24px;
    }
  \`]
})
export class ${componentName} {
  // 组件逻辑
}`;
  }

  /**
   * 返回上一页
   */
  goBack(): void {
    window.history.back();
  }

  /**
   * 重新加载页面
   */
  reloadPage(): void {
    window.location.reload();
  }

  /**
   * 报告错误
   */
  reportError(): void {
    const errorInfo = {
      path: this.routePath,
      component: this.componentName,
      time: this.errorTime,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.error('组件加载错误报告:', errorInfo);

    // 这里可以发送错误报告到服务器
    // this.errorReportService.reportError(errorInfo);

    alert('错误信息已记录到控制台，请联系技术支持。');
  }

  /**
   * 将字符串转换为短横线命名
   */
  private kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  /**
   * 将字符串转换为帕斯卡命名
   */
  private pascalCase(str: string): string {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }
}
