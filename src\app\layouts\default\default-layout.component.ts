import { Component, inject, signal, computed, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';

import { AuthService, MenuService } from '../../core/services'; // 新增 MenuService
import { MenuItem, BreadcrumbItem } from '../../core/types';

@Component({
  selector: 'app-default-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    NzLayoutModule,
    NzMenuModule,
    NzIconModule,
    NzBreadCrumbModule,
    NzAvatarModule,
    NzButtonModule,
    NzSpaceModule,
    NzDropDownModule
  ],
  templateUrl: './default-layout.component.html'
})
export class DefaultLayoutComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly menuService = inject(MenuService); // 新增

  // 状态管理
  readonly isCollapsed = signal(false);
  readonly dynamicMenuItems = signal<MenuItem[]>([]);
  readonly breadcrumbs = signal<BreadcrumbItem[]>([]);

  // 计算属性
  readonly userInfo = computed(() => this.authService.userInfo());
  readonly showSidebar = computed(() => true); // 所有用户都显示侧边栏
  readonly dashboardPath = computed(() => '/admin/dashboard');
  readonly pageTitle = computed(() => '后台管理系统'); // 统一标题
  readonly footerText = computed(() => 'Management System'); // 统一页脚文本


  // 响应式断点
  private readonly breakpoint = 768;
  isVisible: boolean = true;

  ngOnInit(): void {
    this.setupBreadcrumbs();
    this.loadDynamicMenuItems();
    this.checkScreenWidth();
  }

  toggleCollapsed(): void {
    this.isCollapsed.update(collapsed => !collapsed);
  }

  onCollapsedChange(collapsed: boolean): void {
    this.isCollapsed.set(collapsed);
  }

  logout(): void {
    this.authService.logout().subscribe();
  }

  private setupBreadcrumbs(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updateBreadcrumbs();
    });
  }

  private updateBreadcrumbs(): void {
    const url = this.router.url;
    const breadcrumbs: BreadcrumbItem[] = [
      {
        title: '首页',
        path: this.dashboardPath(),
        clickable: true
      }
    ];


    this.breadcrumbs.set(breadcrumbs);
  }

  private loadDynamicMenuItems(): void {
    this.menuService.getMenuItems().subscribe(data => {
      this.dynamicMenuItems.set(data);
    });
  }

  // 监听屏幕尺寸变化
  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    this.checkScreenWidth();
  }

  private checkScreenWidth(): void {
    // 获取当前窗口宽度
    const currentWidth = window.innerWidth;
    // 根据宽度设置 isButtonVisible 的值
    this.isVisible = currentWidth >= this.breakpoint;
  }

}
