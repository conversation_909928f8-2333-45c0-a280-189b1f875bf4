import { Injectable, inject } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { AuthService } from '../services/auth.service';
import { DEFAULT_ROUTES } from '../constants';

/**
 * 认证守卫服务
 *
 * 负责保护需要用户认证的路由，确保只有已登录的用户才能访问受保护的页面。
 * 实现了 CanActivate 和 CanActivateChild 接口，可以保护父路由和子路由。
 *
 * @description
 * 该守卫的工作流程：
 * 1. 检查路由配置中的 requireAuth 标志
 * 2. 如果不需要认证，直接允许访问
 * 3. 如果需要认证，检查用户当前认证状态
 * 4. 如果已认证，允许访问
 * 5. 如果未认证，重定向到登录页面并保存返回URL
 *
 * @example
 * ```typescript
 * // 在路由配置中使用
 * {
 *   path: 'dashboard',
 *   component: DashboardComponent,
 *   canActivate: [AuthGuard],
 *   data: { requireAuth: true }
 * }
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {
  /** 认证服务实例，用于检查用户认证状态 */
  private readonly authService = inject(AuthService);

  /** 路由服务实例，用于导航和重定向 */
  private readonly router = inject(Router);

  /**
   * 路由激活守卫
   *
   * 在路由激活之前调用，用于检查用户是否有权限访问该路由。
   *
   * @param route - 当前激活的路由快照，包含路由参数和数据
   * @param state - 当前路由状态快照，包含完整的URL信息
   * @returns Observable<boolean> - 返回是否允许激活路由的Observable
   *
   * @example
   * ```typescript
   * // Angular 会自动调用此方法，无需手动调用
   * // 路由配置示例：
   * {
   *   path: 'protected',
   *   component: ProtectedComponent,
   *   canActivate: [AuthGuard]
   * }
   * ```
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(route, state);
  }

  /**
   * 子路由激活守卫
   *
   * 在子路由激活之前调用，用于检查用户是否有权限访问子路由。
   * 通常用于保护嵌套路由结构中的子路由。
   *
   * @param childRoute - 子路由快照
   * @param state - 当前路由状态快照
   * @returns Observable<boolean> - 返回是否允许激活子路由的Observable
   *
   * @example
   * ```typescript
   * // 保护所有子路由
   * {
   *   path: 'admin',
   *   component: AdminLayoutComponent,
   *   canActivateChild: [AuthGuard],
   *   children: [
   *     { path: 'users', component: UsersComponent },
   *     { path: 'settings', component: SettingsComponent }
   *   ]
   * }
   * ```
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(childRoute, state);
  }

  /**
   * 核心认证检查逻辑
   *
   * 执行实际的认证检查，包括：
   * 1. 检查路由是否需要认证
   * 2. 验证用户当前认证状态
   * 3. 处理认证失败的情况
   *
   * @private
   * @param route - 路由快照，用于获取路由配置数据
   * @param state - 路由状态快照，用于获取当前URL
   * @returns Observable<boolean> - 认证检查结果
   *
   * @description
   * 认证检查的详细流程：
   * 1. 从路由数据中读取 requireAuth 配置（默认为 true）
   * 2. 如果不需要认证，直接返回 true
   * 3. 检查本地认证状态（快速检查）
   * 4. 如果本地状态显示已认证，返回 true
   * 5. 调用服务端验证认证状态（确保令牌有效）
   * 6. 根据验证结果决定是否允许访问或重定向到登录页
   */
  private checkAuth(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // 检查路由是否需要认证
    // 从路由数据中获取 requireAuth 配置，默认为 true（需要认证）
    const requireAuth = route.data?.['requireAuth'] ?? true;

    // 如果路由不需要认证（如登录页、注册页等公开页面），直接允许访问
    if (!requireAuth) {
      return of(true);
    }

    // 快速检查：如果用户已经认证（本地状态检查），直接允许访问
    // 这是一个性能优化，避免每次都进行网络请求
    if (this.authService.isAuthenticated()) {
      return of(true);
    }

    // 深度验证：调用服务端验证认证状态
    // 这确保了令牌的有效性和用户状态的准确性
    return this.authService.checkAuthStatus().pipe(
      map(isAuthenticated => {
        if (isAuthenticated) {
          // 认证有效，允许访问
          return true;
        } else {
          // 认证无效，重定向到登录页
          // 保存当前尝试访问的URL，登录成功后可以重定向回来
          const returnUrl = state.url;
          this.router.navigate([DEFAULT_ROUTES.LOGIN], {
            queryParams: { returnUrl }
          });
          return false;
        }
      }),
      catchError(() => {
        // 认证检查过程中发生错误（如网络错误）
        // 为了安全起见，重定向到登录页面
        const returnUrl = state.url;
        this.router.navigate([DEFAULT_ROUTES.LOGIN], {
          queryParams: { returnUrl }
        });
        return of(false);
      })
    );
  }
}
