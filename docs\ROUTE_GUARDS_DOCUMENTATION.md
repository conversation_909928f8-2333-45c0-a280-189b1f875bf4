# 路由守卫/拦截器文档

## 概述

本文档详细介绍了 Angular 项目中的路由守卫系统，包括认证守卫、权限守卫的实现原理、使用方法和最佳实践。

## 目录

- [路由守卫概述](#路由守卫概述)
- [AuthGuard - 认证守卫](#authguard---认证守卫)
- [PermissionGuard - 权限守卫](#permissionguard---权限守卫)
- [守卫执行流程](#守卫执行流程)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 路由守卫概述

项目中实现了两个主要的路由守卫：

1. **AuthGuard** - 负责用户认证检查
2. **PermissionGuard** - 负责用户权限和角色检查

这些守卫可以单独使用，也可以组合使用，为应用提供多层次的安全保护。

## AuthGuard - 认证守卫

### 功能说明

`AuthGuard` 负责检查用户是否已经通过认证，确保只有已登录的用户才能访问受保护的路由。

### 核心实现

```typescript
@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(route, state);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(childRoute, state);
  }

  private checkAuth(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // 检查路由是否需要认证
    const requireAuth = route.data?.['requireAuth'] ?? true;
    
    if (!requireAuth) {
      return of(true);
    }

    // 检查用户是否已认证
    if (this.authService.isAuthenticated()) {
      return of(true);
    }

    // 验证认证状态
    return this.authService.checkAuthStatus().pipe(
      map(isAuthenticated => {
        if (isAuthenticated) {
          return true;
        } else {
          // 保存当前尝试访问的URL，登录后可以重定向回来
          const returnUrl = state.url;
          this.router.navigate([DEFAULT_ROUTES.LOGIN], {
            queryParams: { returnUrl }
          });
          return false;
        }
      }),
      catchError(() => {
        // 认证检查失败，重定向到登录页
        const returnUrl = state.url;
        this.router.navigate([DEFAULT_ROUTES.LOGIN], {
          queryParams: { returnUrl }
        });
        return of(false);
      })
    );
  }
}
```

### 关键特性

1. **智能认证检查**: 首先检查本地认证状态，然后验证服务器端状态
2. **返回URL保存**: 自动保存用户尝试访问的URL，登录后可重定向回来
3. **可选认证**: 支持通过路由数据配置是否需要认证
4. **错误处理**: 优雅处理认证检查失败的情况

### 配置选项

在路由配置中，可以通过 `data` 属性控制认证行为：

```typescript
{
  path: 'public-page',
  component: PublicPageComponent,
  data: {
    requireAuth: false  // 不需要认证
  }
}
```

## PermissionGuard - 权限守卫

### 功能说明

`PermissionGuard` 负责检查用户是否具有访问特定路由所需的权限或角色，实现细粒度的访问控制。

### 核心实现

```typescript
@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate, CanActivateChild {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermissions(route, state);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermissions(childRoute, state);
  }

  private checkPermissions(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // 获取路由所需的权限和角色
    const requiredPermissions = route.data?.['permissions'] as string[] || [];
    const requiredRoles = route.data?.['roles'] as string[] || [];

    // 如果没有权限要求，允许访问
    if (requiredPermissions.length === 0 && requiredRoles.length === 0) {
      return of(true);
    }

    // 检查用户是否已认证
    if (!this.authService.isAuthenticated()) {
      this.router.navigate([DEFAULT_ROUTES.LOGIN], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    }

    // 检查权限
    const hasRequiredPermissions = requiredPermissions.length === 0 || 
      this.authService.hasAnyPermission(requiredPermissions);

    // 检查角色
    const hasRequiredRoles = requiredRoles.length === 0 || 
      this.authService.hasAnyRole(requiredRoles);

    if (hasRequiredPermissions && hasRequiredRoles) {
      return of(true);
    } else {
      // 权限不足，重定向到403页面
      this.router.navigate([DEFAULT_ROUTES.FORBIDDEN]);
      return of(false);
    }
  }
}
```

### 关键特性

1. **双重检查**: 同时支持权限和角色检查
2. **灵活配置**: 可以只配置权限、只配置角色，或两者都配置
3. **任一匹配**: 使用 `hasAnyPermission` 和 `hasAnyRole`，只要满足任一条件即可
4. **友好错误**: 权限不足时重定向到 403 页面

### 配置选项

在路由配置中，可以通过 `data` 属性配置权限要求：

```typescript
{
  path: 'admin',
  component: AdminDashboardComponent,
  canActivate: [AuthGuard, PermissionGuard],
  data: {
    requireAuth: true,
    roles: ['admin'],                    // 需要管理员角色
    permissions: ['user:view', 'user:edit']  // 需要用户查看和编辑权限
  }
}
```

## 守卫执行流程

### 执行顺序

当用户访问受保护的路由时，守卫按以下顺序执行：

1. **AuthGuard 执行**
   - 检查路由是否需要认证
   - 验证用户认证状态
   - 如果未认证，重定向到登录页

2. **PermissionGuard 执行**（仅在 AuthGuard 通过后）
   - 检查用户权限和角色
   - 如果权限不足，重定向到 403 页面

### 流程图

```
用户访问路由
    ↓
AuthGuard 检查
    ↓
需要认证？ → 否 → 允许访问
    ↓ 是
已认证？ → 否 → 重定向到登录页
    ↓ 是
PermissionGuard 检查
    ↓
需要权限？ → 否 → 允许访问
    ↓ 是
有权限？ → 否 → 重定向到 403 页面
    ↓ 是
允许访问
```

### 决策逻辑

1. **认证检查逻辑**:
   ```typescript
   // 1. 检查路由配置
   const requireAuth = route.data?.['requireAuth'] ?? true;
   
   // 2. 如果不需要认证，直接通过
   if (!requireAuth) return true;
   
   // 3. 检查本地认证状态
   if (this.authService.isAuthenticated()) return true;
   
   // 4. 验证服务器端认证状态
   return this.authService.checkAuthStatus();
   ```

2. **权限检查逻辑**:
   ```typescript
   // 1. 获取所需权限和角色
   const requiredPermissions = route.data?.['permissions'] || [];
   const requiredRoles = route.data?.['roles'] || [];
   
   // 2. 如果没有要求，直接通过
   if (requiredPermissions.length === 0 && requiredRoles.length === 0) {
     return true;
   }
   
   // 3. 检查权限和角色
   const hasPermissions = this.authService.hasAnyPermission(requiredPermissions);
   const hasRoles = this.authService.hasAnyRole(requiredRoles);
   
   return hasPermissions && hasRoles;
   ```

## 使用示例

### 基本路由配置

```typescript
// app.routes.ts
export const routes: Routes = [
  // 公开路由 - 不需要认证
  {
    path: 'auth',
    component: AuthLayoutComponent,
    data: {
      requireAuth: false
    },
    children: [
      {
        path: 'login',
        component: LoginComponent,
        data: {
          requireAuth: false
        }
      }
    ]
  },

  // 需要认证的路由
  {
    path: 'dashboard',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard],
    data: {
      requireAuth: true
    },
    children: [
      {
        path: '',
        component: DashboardComponent
      }
    ]
  },

  // 需要特定权限的路由
  {
    path: 'admin',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: {
      requireAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: 'users',
        component: UserManagementComponent,
        data: {
          permissions: ['user:view']
        }
      },
      {
        path: 'settings',
        component: SystemSettingsComponent,
        data: {
          permissions: ['system:config']
        }
      }
    ]
  }
];
```

### 组合使用守卫

```typescript
// 同时使用认证和权限守卫
{
  path: 'sensitive-data',
  component: SensitiveDataComponent,
  canActivate: [AuthGuard, PermissionGuard],
  data: {
    requireAuth: true,
    roles: ['admin', 'manager'],           // 需要管理员或经理角色
    permissions: ['data:view', 'data:export']  // 需要数据查看和导出权限
  }
}

// 只使用认证守卫
{
  path: 'profile',
  component: UserProfileComponent,
  canActivate: [AuthGuard],
  data: {
    requireAuth: true
  }
}

// 子路由继承父路由的守卫
{
  path: 'admin',
  canActivate: [AuthGuard, PermissionGuard],
  canActivateChild: [PermissionGuard],  // 子路由也会检查权限
  data: {
    requireAuth: true,
    roles: ['admin']
  },
  children: [
    {
      path: 'dashboard',
      component: AdminDashboardComponent
    },
    {
      path: 'reports',
      component: ReportsComponent,
      data: {
        permissions: ['report:view']  // 额外的权限要求
      }
    }
  ]
}
```

### 动态权限检查

```typescript
// 在组件中动态检查权限
@Component({
  selector: 'app-user-list',
  template: `
    <div class="user-list">
      <nz-table [nzData]="users">
        <thead>
          <tr>
            <th>用户名</th>
            <th>邮箱</th>
            <th *ngIf="canEdit()">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of users">
            <td>{{ user.username }}</td>
            <td>{{ user.email }}</td>
            <td *ngIf="canEdit()">
              <button nz-button nzType="primary" (click)="editUser(user)">
                编辑
              </button>
              <button
                *ngIf="canDelete()"
                nz-button
                nzType="danger"
                (click)="deleteUser(user)">
                删除
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  `
})
export class UserListComponent {
  private readonly authService = inject(AuthService);

  // 检查是否有编辑权限
  canEdit(): boolean {
    return this.authService.hasPermission('user:edit');
  }

  // 检查是否有删除权限
  canDelete(): boolean {
    return this.authService.hasPermission('user:delete');
  }

  // 检查是否是管理员
  isAdmin(): boolean {
    return this.authService.hasRole('admin');
  }
}
```

## 最佳实践

### 1. 守卫配置原则

```typescript
// ✅ 推荐：明确配置认证要求
{
  path: 'public',
  component: PublicComponent,
  data: {
    requireAuth: false  // 明确标记为公开页面
  }
}

// ❌ 不推荐：依赖默认值
{
  path: 'public',
  component: PublicComponent
  // 没有明确配置，依赖默认的 requireAuth: true
}
```

### 2. 权限粒度设计

```typescript
// ✅ 推荐：细粒度权限
const PERMISSIONS = {
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete'
};

// ❌ 不推荐：粗粒度权限
const PERMISSIONS = {
  USER_MANAGE: 'user:manage'  // 太宽泛
};
```

### 3. 错误处理

```typescript
// ✅ 推荐：优雅的错误处理
private checkAuth(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
  return this.authService.checkAuthStatus().pipe(
    map(isAuthenticated => {
      if (isAuthenticated) {
        return true;
      } else {
        this.handleAuthFailure(state.url);
        return false;
      }
    }),
    catchError(error => {
      console.error('认证检查失败:', error);
      this.handleAuthFailure(state.url);
      return of(false);
    })
  );
}

private handleAuthFailure(returnUrl: string): void {
  this.router.navigate([DEFAULT_ROUTES.LOGIN], {
    queryParams: { returnUrl }
  });
}
```

### 4. 性能优化

```typescript
// ✅ 推荐：缓存认证状态
@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  private authCheckCache = new Map<string, { result: boolean; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟

  private checkAuthWithCache(route: ActivatedRouteSnapshot): Observable<boolean> {
    const cacheKey = this.getCacheKey(route);
    const cached = this.authCheckCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return of(cached.result);
    }

    return this.authService.checkAuthStatus().pipe(
      tap(result => {
        this.authCheckCache.set(cacheKey, {
          result,
          timestamp: Date.now()
        });
      })
    );
  }
}
```

## 常见问题

### Q1: 为什么守卫没有生效？

**A**: 检查以下几点：
1. 确保守卫已在路由配置中正确添加
2. 检查守卫的注入依赖是否正确
3. 验证路由数据配置是否正确

```typescript
// 确保守卫在 canActivate 数组中
{
  path: 'protected',
  component: ProtectedComponent,
  canActivate: [AuthGuard, PermissionGuard],  // 确保这里包含了守卫
  data: {
    requireAuth: true,
    roles: ['user']
  }
}
```

### Q2: 如何处理守卫中的异步操作？

**A**: 使用 Observable 或 Promise：

```typescript
canActivate(): Observable<boolean> {
  return this.authService.validateToken().pipe(
    map(isValid => {
      if (!isValid) {
        this.router.navigate(['/login']);
        return false;
      }
      return true;
    }),
    catchError(() => {
      this.router.navigate(['/login']);
      return of(false);
    })
  );
}
```

### Q3: 如何在守卫中显示加载状态？

**A**: 结合 LoadingService 使用：

```typescript
canActivate(): Observable<boolean> {
  this.loadingService.startLoading('auth-check', '验证用户权限...');

  return this.authService.checkAuthStatus().pipe(
    finalize(() => {
      this.loadingService.stopLoading('auth-check');
    }),
    map(isAuthenticated => {
      // 处理认证结果
      return isAuthenticated;
    })
  );
}
```

### Q4: 如何实现条件性权限检查？

**A**: 使用动态权限配置：

```typescript
// 在路由配置中使用函数
{
  path: 'conditional',
  component: ConditionalComponent,
  canActivate: [ConditionalGuard],
  data: {
    permissionCheck: (user: UserInfo) => {
      // 根据用户信息动态决定权限
      return user.department === 'IT' || user.roles.includes('admin');
    }
  }
}
```

## 扩展功能

### 自定义守卫示例

```typescript
// 时间限制守卫
@Injectable({
  providedIn: 'root'
})
export class TimeRestrictedGuard implements CanActivate {
  canActivate(): boolean {
    const currentHour = new Date().getHours();
    const isBusinessHours = currentHour >= 9 && currentHour <= 17;

    if (!isBusinessHours) {
      // 显示提示信息
      return false;
    }

    return true;
  }
}

// IP 限制守卫
@Injectable({
  providedIn: 'root'
})
export class IpRestrictedGuard implements CanActivate {
  private readonly allowedIPs = ['***********/24', '10.0.0.0/8'];

  canActivate(): Observable<boolean> {
    return this.ipService.getCurrentIP().pipe(
      map(ip => this.isAllowedIP(ip))
    );
  }

  private isAllowedIP(ip: string): boolean {
    // IP 检查逻辑
    return this.allowedIPs.some(range => this.ipInRange(ip, range));
  }
}
```

---

## 总结

路由守卫是 Angular 应用安全性的重要组成部分。通过合理使用 `AuthGuard` 和 `PermissionGuard`，可以实现：

1. **多层次安全保护**: 认证 + 权限双重检查
2. **灵活的配置**: 支持路由级和组件级权限控制
3. **良好的用户体验**: 智能重定向和错误处理
4. **高性能**: 缓存机制和优化策略

遵循本文档中的最佳实践，可以构建安全、高效、易维护的权限控制系统。
