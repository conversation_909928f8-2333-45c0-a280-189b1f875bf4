import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

import { LoadingComponent, ErrorDisplayComponent } from './shared/components';

/**
 * 应用根组件
 *
 * 应用的根组件，负责提供全局布局和基础服务组件。
 * 不再在应用启动时加载动态路由，而是在用户登录成功后按需加载。
 *
 * @description
 * 主要功能：
 * - 提供应用的根布局结构
 * - 集成全局加载状态显示组件
 * - 集成全局错误显示组件
 * - 提供路由出口容器
 *
 * 设计变更：
 * - 移除了应用启动时的动态路由加载逻辑
 * - 动态路由加载现在由认证服务在登录成功后触发
 * - 避免在用户未登录时显示"正在加载路由配置..."提示
 *
 * @example
 * ```typescript
 * // 在 main.ts 中引导应用
 * bootstrapApplication(App, {
 *   providers: [
 *     // 应用配置
 *   ]
 * });
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    LoadingComponent,
    ErrorDisplayComponent
  ],
  template: `
    <div class="app-container">
      <router-outlet></router-outlet>

      <!-- 全局加载组件 -->
      <app-loading></app-loading>

      <!-- 全局错误显示组件 -->
      <app-error-display></app-error-display>
    </div>
  `,
  styles: [`
    .app-container {
      width: 100%;
      height: 100vh;
    }
  `]
})
export class App {
  // 移除了 OnInit 接口和相关的动态路由初始化逻辑
  // 动态路由加载现在由认证服务在登录成功后处理
}
