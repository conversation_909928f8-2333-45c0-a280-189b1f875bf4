import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzProgressModule } from 'ng-zorro-antd/progress';

import { LoadingService } from '../../../core/services/loading.service';

/**
 * 全局加载组件
 *
 * 提供全局的加载指示器，自动响应 LoadingService 的状态变化。
 * 支持加载消息显示和进度条展示。
 *
 * @description
 * 主要功能：
 * - 全局加载状态显示
 * - 自动响应加载状态变化
 * - 支持加载消息和进度显示
 * - 覆盖整个视口的加载遮罩
 *
 * 设计特点：
 * - 使用 Angular Standalone Component
 * - 集成 NG-ZORRO Spin 和 Progress 组件
 * - 使用 Signal 进行响应式状态管理
 * - 固定定位覆盖整个屏幕
 *
 * @example
 * ```typescript
 * // 在应用根组件中使用
 * @Component({
 *   template: `
 *     <router-outlet></router-outlet>
 *     <app-loading></app-loading>
 *   `
 * })
 * export class AppComponent {}
 *
 * // 在布局组件中使用
 * @Component({
 *   template: `
 *     <div class="layout">
 *       <ng-content></ng-content>
 *       <app-loading></app-loading>
 *     </div>
 *   `
 * })
 * export class LayoutComponent {}
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [
    CommonModule,
    NzSpinModule,
    NzProgressModule
  ],
  template: `
    @if (isLoading()) {
      <div class="loading-overlay">
        <div class="loading-content">
          <nz-spin nzSize="large" [nzTip]="loadingMessage()">
            @if (hasProgress()) {
              <div class="loading-progress">
                <nz-progress 
                  [nzPercent]="progress()" 
                  nzStatus="active"
                  [nzShowInfo]="true">
                </nz-progress>
              </div>
            }
          </nz-spin>
        </div>
      </div>
    }
  `,
  styles: [`
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-content {
      text-align: center;
      padding: 24px;
      min-width: 200px;
    }
    
    .loading-progress {
      margin-top: 16px;
      width: 200px;
    }
  `]
})
export class LoadingComponent {
  // ==================== 依赖注入 ====================

  /**
   * 加载状态服务实例
   *
   * 用于获取全局加载状态和相关信息。
   *
   * @private
   * @readonly
   */
  private readonly loadingService = inject(LoadingService);

  // ==================== 计算属性 ====================

  /**
   * 是否正在加载的计算属性
   *
   * 基于 LoadingService 的全局加载状态自动计算。
   *
   * @readonly
   * @returns boolean - 是否有任何加载操作正在进行
   *
   * @example
   * ```typescript
   * // 在模板中使用
   * @if (isLoading()) {
   *   <div class="loading-overlay">...</div>
   * }
   * ```
   */
  readonly isLoading = computed(() => this.loadingService.isAnyLoading());

  /**
   * 加载消息的计算属性
   *
   * 获取当前加载操作的消息，如果没有消息则显示默认文本。
   *
   * @readonly
   * @returns string - 加载消息文本
   *
   * @example
   * ```typescript
   * // 在模板中显示
   * <nz-spin [nzTip]="loadingMessage()">
   * ```
   */
  readonly loadingMessage = computed(() => this.loadingService.globalLoadingMessage() || '加载中...');

  /**
   * 是否有进度信息的计算属性
   *
   * 检查当前是否有任何加载操作包含进度信息。
   *
   * @readonly
   * @returns boolean - 是否有进度信息
   *
   * @example
   * ```typescript
   * // 在模板中条件显示进度条
   * @if (hasProgress()) {
   *   <nz-progress [nzPercent]="progress()"></nz-progress>
   * }
   * ```
   */
  readonly hasProgress = computed(() => {
    const states = this.loadingService.loadingStates();
    return Array.from(states.values()).some(state =>
      state.loading && typeof state.progress === 'number'
    );
  });

  /**
   * 当前进度百分比的计算属性
   *
   * 获取第一个包含进度信息的加载操作的进度百分比。
   *
   * @readonly
   * @returns number - 进度百分比（0-100）
   *
   * @example
   * ```typescript
   * // 在模板中显示进度
   * <nz-progress [nzPercent]="progress()" nzStatus="active"></nz-progress>
   * ```
   */
  readonly progress = computed(() => {
    const states = this.loadingService.loadingStates();
    const stateWithProgress = Array.from(states.values()).find(state =>
      state.loading && typeof state.progress === 'number'
    );
    return stateWithProgress?.progress || 0;
  });
}
