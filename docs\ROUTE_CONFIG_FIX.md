# 动态路由配置错误修复文档

## 问题描述

在登录后加载动态路由时出现以下错误：

```
RuntimeError: NG04014: Invalid configuration of route 'view/products': redirectTo and children cannot be used together
```

## 错误原因

Angular 路由系统不允许在同一个路由配置中同时使用 `redirectTo` 和 `children` 属性。这是 Angular 的设计限制，因为：

1. `redirectTo` 表示将当前路由重定向到另一个路由
2. `children` 表示当前路由有子路由
3. 这两个概念在逻辑上是冲突的

## 修复方案

### 1. 修复 mock-api.service.ts 中的路由配置

**修复前的错误配置：**
```typescript
{
  id: 'dynamic-products',
  path: 'products',
  title: '产品管理',
  redirectTo: 'products/list',  // ❌ 错误：与 children 冲突
  pathMatch: 'full',
  children: [                   // ❌ 错误：与 redirectTo 冲突
    // ... 子路由
  ]
}
```

**修复后的正确配置：**
```typescript
{
  id: 'dynamic-products',
  path: 'products',
  title: '产品管理',
  children: [
    {
      id: 'products-index',
      path: '',                 // ✅ 空路径匹配父路由
      title: '产品管理',
      redirectTo: 'list',       // ✅ 在子路由中使用重定向
      pathMatch: 'full'
    },
    {
      id: 'products-list',
      path: 'list',
      // ... 其他配置
    }
    // ... 其他子路由
  ]
}
```

### 2. 修复 dynamic-route.service.ts 中的路由转换逻辑

在 `convertToAngularRoute` 方法中添加了安全检查：

```typescript
// 处理重定向配置 - 注意：redirectTo 和 children 不能同时使用
if (routeConfig.redirectTo && (!routeConfig.children || routeConfig.children.length === 0)) {
  route.redirectTo = routeConfig.redirectTo;
  route.pathMatch = routeConfig.pathMatch || 'full';
}
```

## 修复效果

1. **消除了路由配置错误**：不再出现 NG04014 错误
2. **保持了预期的路由行为**：访问 `/view/products` 仍然会重定向到 `/view/products/list`
3. **提高了代码健壮性**：添加了防护逻辑避免类似错误

## 路由结构说明

修复后的路由结构：
```
/view/products
├── '' (空路径) → 重定向到 'list'
├── list → ProductListComponent
└── categories → ProductCategoriesComponent
```

访问路径映射：
- `/view/products` → 自动重定向到 `/view/products/list`
- `/view/products/list` → 显示产品列表页面
- `/view/products/categories` → 显示产品分类页面

## 最佳实践

1. **避免在父路由中同时使用 redirectTo 和 children**
2. **使用空路径子路由实现默认重定向**：
   ```typescript
   children: [
     { path: '', redirectTo: 'default-child', pathMatch: 'full' },
     { path: 'default-child', component: DefaultComponent }
   ]
   ```
3. **在路由转换逻辑中添加验证**，确保配置的有效性

## 相关文件

- `src/app/core/services/mock-api.service.ts` - 修复路由配置
- `src/app/core/services/dynamic-route.service.ts` - 修复转换逻辑
- `docs/ROUTE_CONFIG_FIX.md` - 本文档

## 测试验证

修复后应用程序能够：
1. 正常启动开发服务器
2. 成功加载动态路由
3. 正确处理路由重定向
4. 无 Angular 路由配置错误
