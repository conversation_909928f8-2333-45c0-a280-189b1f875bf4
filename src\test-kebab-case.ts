/**
 * 测试 kebabCase 函数
 */

function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .toLowerCase()
    .replace(/component$/, '')
    .replace(/-$/, ''); // 移除末尾的连字符
}

// 测试用例
console.log('测试 kebabCase 函数:');
console.log('ProductListComponent ->', kebabCase('ProductListComponent'));
console.log('ProductCategoriesComponent ->', kebabCase('ProductCategoriesComponent'));
console.log('UserManagementComponent ->', kebabCase('UserManagementComponent'));
console.log('DashboardComponent ->', kebabCase('DashboardComponent'));

// 预期结果:
// ProductListComponent -> product-list
// ProductCategoriesComponent -> product-categories
// UserManagementComponent -> user-management
// DashboardComponent -> dashboard
