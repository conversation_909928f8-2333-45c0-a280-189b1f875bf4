import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';

import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzCardModule } from 'ng-zorro-antd/card';

@Component({
  selector: 'app-auth-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NzLayoutModule,
    NzCardModule
  ],
  template: `
    <nz-layout class="auth-layout">
      <nz-content class="auth-content">
        <div class="auth-container">
          <!-- 左侧背景区域 -->
          <div class="auth-background">
            <div class="background-overlay">
              <div class="brand-info">
                <img src="/assets/logo.svg" alt="Logo" class="brand-logo">
                <h1 class="brand-title">企业管理系统</h1>
                <p class="brand-description">
                  基于 Angular 和 NG-ZORRO 构建的现代化企业级管理平台
                </p>
              </div>
            </div>
          </div>
          
          <!-- 右侧表单区域 -->
          <div class="auth-form-container">
            <div class="auth-form-wrapper">
              <router-outlet></router-outlet>
            </div>
          </div>
        </div>
      </nz-content>
    </nz-layout>
  `,
  styles: [`
    .auth-layout {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .auth-content {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 24px;
    }
    
    .auth-container {
      display: flex;
      width: 100%;
      max-width: 1000px;
      min-height: 600px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .auth-background {
      flex: 1;
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .background-overlay {
      position: relative;
      z-index: 2;
      text-align: center;
      color: white;
      padding: 40px;
    }
    
    .background-overlay::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('/assets/auth-pattern.svg') no-repeat center center;
      background-size: cover;
      opacity: 0.1;
      z-index: -1;
    }
    
    .brand-logo {
      height: 80px;
      margin-bottom: 24px;
    }
    
    .brand-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 16px;
      color: white;
    }
    
    .brand-description {
      font-size: 16px;
      line-height: 1.6;
      opacity: 0.9;
      max-width: 300px;
      margin: 0 auto;
    }
    
    .auth-form-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
      background: white;
    }
    
    .auth-form-wrapper {
      width: 100%;
      max-width: 400px;
    }
    
    @media (max-width: 768px) {
      .auth-container {
        flex-direction: column;
        max-width: 400px;
        min-height: auto;
      }
      
      .auth-background {
        min-height: 200px;
      }
      
      .brand-title {
        font-size: 24px;
      }
      
      .brand-description {
        font-size: 14px;
      }
      
      .auth-form-container {
        padding: 24px;
      }
    }
    
    @media (max-width: 480px) {
      .auth-content {
        padding: 12px;
      }
      
      .auth-container {
        border-radius: 8px;
      }
      
      .auth-form-container {
        padding: 20px;
      }
    }
  `]
})
export class AuthLayoutComponent { }
