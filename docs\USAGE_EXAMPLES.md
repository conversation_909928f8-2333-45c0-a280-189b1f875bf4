# 使用示例

本文档提供了 Angular 企业级动态路由系统的详细使用示例。

## 目录

- [基础使用](#基础使用)
- [动态路由管理](#动态路由管理)
- [权限控制](#权限控制)
- [布局管理](#布局管理)
- [错误处理](#错误处理)
- [高级用法](#高级用法)

## 基础使用

### 1. 用户登录

```typescript
import { AuthService } from './core/services';

@Component({...})
export class LoginComponent {
  private readonly authService = inject(AuthService);

  async login(credentials: {username: string, password: string}) {
    try {
      const userInfo = await this.authService.login(credentials).toPromise();
      console.log('登录成功:', userInfo);
      
      // 根据用户角色重定向
      if (userInfo.roles.some(role => role.name === 'admin')) {
        this.router.navigate(['/admin/dashboard']);
      } else {
        this.router.navigate(['/user/dashboard']);
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  }
}
```

### 2. 检查认证状态

```typescript
import { AuthService } from './core/services';

@Component({...})
export class AppComponent implements OnInit {
  private readonly authService = inject(AuthService);

  ngOnInit() {
    // 检查用户是否已登录
    if (this.authService.isAuthenticated()) {
      console.log('用户已登录');
    } else {
      this.router.navigate(['/auth/login']);
    }
  }
}
```

## 动态路由管理

### 1. 加载动态路由

```typescript
import { DynamicRouteService } from './core/services';

@Component({...})
export class AdminComponent implements OnInit {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  ngOnInit() {
    // 从API加载路由配置
    this.dynamicRouteService.loadRoutesFromAPI().subscribe({
      next: (routes) => {
        console.log('路由加载成功:', routes);
      },
      error: (error) => {
        console.error('路由加载失败:', error);
      }
    });
  }
}
```

### 2. 动态添加路由

```typescript
import { DynamicRouteService, LayoutType } from './core/services';

@Component({...})
export class RouteManagerComponent {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  addNewRoute() {
    const routeConfig: DynamicRouteConfig = {
      id: 'reports',
      path: 'reports',
      title: '报表管理',
      layout: LayoutType.ADMIN,
      requireAuth: true,
      permissions: ['reports:view'],
      loadComponent: () => import('./pages/reports/reports.component').then(m => m.ReportsComponent),
      menu: {
        id: 'reports',
        title: '报表管理',
        icon: 'bar-chart',
        path: '/admin/reports'
      }
    };

    this.dynamicRouteService.addRoute(routeConfig).subscribe({
      next: () => {
        console.log('路由添加成功');
        // 可以重新导航到新路由
        this.router.navigate(['/admin/reports']);
      },
      error: (error) => {
        console.error('路由添加失败:', error);
      }
    });
  }
}
```

### 3. 删除动态路由

```typescript
removeRoute(routeId: string) {
  this.dynamicRouteService.removeRoute(routeId).subscribe({
    next: () => {
      console.log('路由删除成功');
    },
    error: (error) => {
      console.error('路由删除失败:', error);
    }
  });
}
```

### 4. 更新路由配置

```typescript
updateRoute() {
  const updatedConfig: DynamicRouteConfig = {
    id: 'reports',
    path: 'reports',
    title: '高级报表管理', // 更新标题
    layout: LayoutType.ADMIN,
    requireAuth: true,
    permissions: ['reports:view', 'reports:advanced'], // 添加新权限
    loadComponent: () => import('./pages/advanced-reports/advanced-reports.component')
  };

  this.dynamicRouteService.updateRoute(updatedConfig).subscribe({
    next: () => {
      console.log('路由更新成功');
    },
    error: (error) => {
      console.error('路由更新失败:', error);
    }
  });
}
```

## 权限控制

### 1. 基于权限的组件显示

```typescript
import { AuthService } from './core/services';

@Component({
  template: `
    <div>
      <!-- 只有具有创建权限的用户才能看到此按钮 -->
      @if (canCreate()) {
        <button nz-button nzType="primary" (click)="createUser()">
          创建用户
        </button>
      }
      
      <!-- 只有管理员才能看到此区域 -->
      @if (isAdmin()) {
        <div class="admin-panel">
          <h3>管理员面板</h3>
          <!-- 管理员功能 -->
        </div>
      }
    </div>
  `
})
export class UserManagementComponent {
  private readonly authService = inject(AuthService);

  canCreate = computed(() => 
    this.authService.hasPermission('user:create')
  );

  isAdmin = computed(() => 
    this.authService.hasRole('admin')
  );

  createUser() {
    // 创建用户逻辑
  }
}
```

### 2. 路由级权限控制

```typescript
// 在路由配置中设置权限
const routes: Routes = [
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: { 
      requireAuth: true,
      roles: ['admin'],
      permissions: ['admin:access']
    },
    children: [
      {
        path: 'users',
        component: UserManagementComponent,
        data: { 
          permissions: ['user:view']
        }
      },
      {
        path: 'system',
        component: SystemConfigComponent,
        data: { 
          permissions: ['system:config']
        }
      }
    ]
  }
];
```

### 3. 动态权限检查

```typescript
@Component({...})
export class DynamicPermissionComponent implements OnInit {
  private readonly authService = inject(AuthService);
  
  userPermissions = computed(() => this.authService.userPermissions());
  
  ngOnInit() {
    // 监听权限变化
    this.authService.userInfo$.subscribe(userInfo => {
      if (userInfo) {
        this.updateUIBasedOnPermissions(userInfo.permissions);
      }
    });
  }

  private updateUIBasedOnPermissions(permissions: string[]) {
    // 根据权限动态更新UI
    if (permissions.includes('advanced:features')) {
      this.enableAdvancedFeatures();
    }
  }

  checkMultiplePermissions() {
    const requiredPermissions = ['user:create', 'user:update'];
    
    if (this.authService.hasAllPermissions(requiredPermissions)) {
      console.log('用户具有所有必需权限');
    } else if (this.authService.hasAnyPermission(requiredPermissions)) {
      console.log('用户具有部分权限');
    } else {
      console.log('用户没有任何必需权限');
    }
  }
}
```

## 布局管理

### 1. 动态切换布局

```typescript
import { LayoutService, LayoutType } from './core/services';

@Component({...})
export class LayoutControlComponent {
  private readonly layoutService = inject(LayoutService);

  switchToAdminLayout() {
    this.layoutService.setLayout(LayoutType.ADMIN, {
      showSidebar: true,
      showBreadcrumb: true,
      sidebarCollapsible: true,
      sidebarCollapsed: false
    });
  }

  switchToUserLayout() {
    this.layoutService.setLayout(LayoutType.USER, {
      showHeader: true,
      showBreadcrumb: true,
      showFooter: true
    });
  }

  switchToFullScreenLayout() {
    this.layoutService.setLayout(LayoutType.FULL, {
      showHeader: false,
      showSidebar: false,
      showBreadcrumb: false,
      showFooter: false
    });
  }
}
```

### 2. 响应式布局配置

```typescript
@Component({...})
export class ResponsiveLayoutComponent implements OnInit {
  private readonly layoutService = inject(LayoutService);

  ngOnInit() {
    // 监听屏幕尺寸变化
    this.setupResponsiveLayout();
  }

  private setupResponsiveLayout() {
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    
    const handleScreenChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        // 移动端布局
        this.layoutService.updateLayoutConfig({
          sidebarCollapsed: true,
          showBreadcrumb: false
        });
      } else {
        // 桌面端布局
        this.layoutService.updateLayoutConfig({
          sidebarCollapsed: false,
          showBreadcrumb: true
        });
      }
    };

    mediaQuery.addEventListener('change', handleScreenChange);
    handleScreenChange(mediaQuery as any);
  }
}
```

### 3. 自定义布局组件

```typescript
@Component({
  selector: 'app-dashboard-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, NzLayoutModule],
  template: `
    <nz-layout class="dashboard-layout">
      <nz-header class="dashboard-header">
        <div class="header-content">
          <h1>仪表盘</h1>
          <div class="header-actions">
            <!-- 头部操作按钮 -->
          </div>
        </div>
      </nz-header>
      
      <nz-content class="dashboard-content">
        <div class="content-wrapper">
          <router-outlet></router-outlet>
        </div>
      </nz-content>
    </nz-layout>
  `,
  styles: [`
    .dashboard-layout {
      min-height: 100vh;
    }
    
    .dashboard-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
    }
    
    .dashboard-content {
      padding: 24px;
    }
    
    .content-wrapper {
      background: white;
      border-radius: 8px;
      padding: 24px;
      min-height: calc(100vh - 120px);
    }
  `]
})
export class DashboardLayoutComponent {}

// 注册自定义布局
@Injectable()
export class CustomLayoutService {
  constructor(private layoutService: LayoutService) {
    this.registerCustomLayouts();
  }

  private registerCustomLayouts() {
    this.layoutService.registerLayoutComponent(
      'dashboard' as LayoutType,
      DashboardLayoutComponent
    );
  }
}
```

## 错误处理

### 1. 全局错误处理

```typescript
import { ErrorService, LoadingService } from './core/services';

@Component({...})
export class DataManagementComponent {
  private readonly errorService = inject(ErrorService);
  private readonly loadingService = inject(LoadingService);

  async loadData() {
    try {
      this.loadingService.startLoading('data-loading', '正在加载数据...');
      
      const data = await this.dataService.getData().toPromise();
      
      this.loadingService.stopLoading('data-loading');
      console.log('数据加载成功:', data);
      
    } catch (error) {
      this.loadingService.stopLoading('data-loading');
      
      this.errorService.addError(
        'data-loading-error',
        '数据加载失败',
        error.message,
        'error'
      );
    }
  }

  showWarning() {
    this.errorService.addError(
      'warning-example',
      '这是一个警告消息',
      '某些功能可能不可用',
      'warning'
    );
  }

  showInfo() {
    this.errorService.addError(
      'info-example',
      '信息提示',
      '操作已成功完成',
      'info'
    );
  }
}
```

### 2. 错误恢复机制

```typescript
@Component({...})
export class ErrorRecoveryComponent {
  private readonly errorService = inject(ErrorService);
  
  retryOperation() {
    // 清除之前的错误
    this.errorService.removeError('operation-error');
    
    // 重试操作
    this.performOperation().catch(error => {
      this.errorService.addError(
        'operation-error',
        '操作失败',
        `错误详情: ${error.message}`,
        'error'
      );
    });
  }

  private async performOperation() {
    // 执行可能失败的操作
    throw new Error('模拟操作失败');
  }
}
```

## 高级用法

### 1. 路由预加载策略

```typescript
import { PreloadingStrategy, Route } from '@angular/router';
import { Observable, of } from 'rxjs';

export class CustomPreloadingStrategy implements PreloadingStrategy {
  preload(route: Route, fn: () => Observable<any>): Observable<any> {
    // 根据路由数据决定是否预加载
    if (route.data?.['preload']) {
      console.log('预加载路由:', route.path);
      return fn();
    }
    return of(null);
  }
}

// 在路由配置中使用
const routes: Routes = [
  {
    path: 'admin',
    loadComponent: () => import('./admin/admin.component'),
    data: { preload: true } // 标记为预加载
  }
];
```

### 2. 路由数据传递

```typescript
@Component({...})
export class RouteDataComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);

  ngOnInit() {
    // 获取路由数据
    const routeData = this.route.snapshot.data;
    console.log('路由数据:', routeData);

    // 监听路由数据变化
    this.route.data.subscribe(data => {
      this.handleRouteData(data);
    });
  }

  private handleRouteData(data: any) {
    if (data.title) {
      document.title = data.title;
    }
    
    if (data.permissions) {
      this.checkPermissions(data.permissions);
    }
  }
}
```

### 3. 动态菜单生成

```typescript
@Component({...})
export class DynamicMenuComponent implements OnInit {
  private readonly dynamicRouteService = inject(DynamicRouteService);
  private readonly authService = inject(AuthService);

  menuItems = signal<MenuItem[]>([]);

  ngOnInit() {
    this.generateMenuFromRoutes();
  }

  private generateMenuFromRoutes() {
    this.dynamicRouteService.routeConfigs$.subscribe(routes => {
      const userPermissions = this.authService.userPermissions();
      const filteredMenuItems = this.filterMenuByPermissions(routes, userPermissions);
      this.menuItems.set(filteredMenuItems);
    });
  }

  private filterMenuByPermissions(routes: DynamicRouteConfig[], permissions: string[]): MenuItem[] {
    return routes
      .filter(route => route.menu && this.hasRequiredPermissions(route, permissions))
      .map(route => route.menu!)
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  private hasRequiredPermissions(route: DynamicRouteConfig, userPermissions: string[]): boolean {
    if (!route.permissions || route.permissions.length === 0) {
      return true;
    }
    return route.permissions.some(permission => userPermissions.includes(permission));
  }
}
```

这些示例展示了如何在实际项目中使用动态路由系统的各种功能。您可以根据具体需求调整和扩展这些示例。
