import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * 加载状态接口
 *
 * 定义单个加载状态的数据结构。
 *
 * @interface LoadingState
 */
export interface LoadingState {
  /** 加载状态的唯一标识符 */
  key: string;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载提示消息 */
  message?: string;
  /** 加载进度百分比（0-100） */
  progress?: number;
}

/**
 * 加载状态管理服务
 *
 * 提供全局的加载状态管理功能，支持多个并发加载状态的管理。
 * 使用 Signal 和 Observable 双重状态管理模式。
 *
 * @description
 * 核心功能：
 * - 多实例加载状态管理
 * - 进度跟踪和消息提示
 * - 全局加载状态计算
 * - 响应式状态更新
 *
 * 设计特点：
 * - 使用 Map 管理多个加载状态
 * - 支持进度百分比显示
 * - 提供计算属性自动判断全局加载状态
 * - 使用 Signal 提供现代化的响应式接口
 *
 * @example
 * ```typescript
 * // 基本使用
 * constructor(private loadingService: LoadingService) {}
 *
 * // 开始加载
 * this.loadingService.startLoading('api-call', '正在加载数据...');
 *
 * // 更新进度
 * this.loadingService.updateProgress('api-call', 50, '加载中... 50%');
 *
 * // 停止加载
 * this.loadingService.stopLoading('api-call');
 *
 * // 检查加载状态
 * const isLoading = this.loadingService.isLoading('api-call');
 * const isAnyLoading = this.loadingService.isAnyLoading();
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  // ==================== 状态管理 ====================

  /**
   * 加载状态的 BehaviorSubject
   *
   * 使用 Map 存储多个加载状态，支持并发管理。
   *
   * @private
   */
  private readonly loadingStatesSubject = new BehaviorSubject<Map<string, LoadingState>>(new Map());

  /**
   * 加载状态的 Signal
   *
   * 使用 Angular Signal 提供现代化的响应式状态管理。
   *
   * @private
   */
  private readonly loadingStatesSignal = signal<Map<string, LoadingState>>(new Map());

  // ==================== 公开状态接口 ====================

  /**
   * 加载状态的 Observable 流
   *
   * 提供加载状态变更的订阅接口。
   *
   * @readonly
   */
  readonly loadingStates$ = this.loadingStatesSubject.asObservable();

  /**
   * 加载状态的 Signal（只读）
   *
   * 提供加载状态的 Signal 访问接口。
   *
   * @readonly
   */
  readonly loadingStates = this.loadingStatesSignal.asReadonly();

  // ==================== 计算属性 ====================

  /**
   * 是否有任何加载状态的计算属性
   *
   * 自动计算是否存在任何正在进行的加载操作。
   *
   * @readonly
   * @returns boolean - 是否有任何加载状态
   *
   * @example
   * ```typescript
   * // 在组件中使用
   * readonly showGlobalLoading = this.loadingService.isAnyLoading;
   *
   * // 在模板中使用
   * @if (loadingService.isAnyLoading()) {
   *   <div class="global-loading">加载中...</div>
   * }
   * ```
   */
  readonly isAnyLoading = computed(() => {
    const states = this.loadingStates();
    return Array.from(states.values()).some(state => state.loading);
  });

  /**
   * 全局加载消息的计算属性
   *
   * 自动获取当前正在加载的第一个状态的消息。
   *
   * @readonly
   * @returns string - 全局加载消息
   *
   * @example
   * ```typescript
   * // 在组件中使用
   * readonly globalMessage = this.loadingService.globalLoadingMessage;
   *
   * // 在模板中显示
   * <div class="loading-message">{{ loadingService.globalLoadingMessage() }}</div>
   * ```
   */
  readonly globalLoadingMessage = computed(() => {
    const states = this.loadingStates();
    const loadingState = Array.from(states.values()).find(state => state.loading);
    return loadingState?.message || '';
  });

  /**
   * 开始加载
   */
  startLoading(key: string, message?: string, progress?: number): void {
    const currentStates = new Map(this.loadingStatesSignal());
    currentStates.set(key, {
      key,
      loading: true,
      message,
      progress
    });

    this.updateStates(currentStates);
  }

  /**
   * 停止加载
   */
  stopLoading(key: string): void {
    const currentStates = new Map(this.loadingStatesSignal());
    currentStates.delete(key);

    this.updateStates(currentStates);
  }

  /**
   * 更新加载进度
   */
  updateProgress(key: string, progress: number, message?: string): void {
    const currentStates = new Map(this.loadingStatesSignal());
    const existingState = currentStates.get(key);

    if (existingState) {
      currentStates.set(key, {
        ...existingState,
        progress,
        message: message || existingState.message
      });

      this.updateStates(currentStates);
    }
  }

  /**
   * 检查指定键是否正在加载
   */
  isLoading(key: string): boolean {
    const states = this.loadingStatesSignal();
    const state = states.get(key);
    return state?.loading ?? false;
  }

  /**
   * 获取指定键的加载状态
   */
  getLoadingState(key: string): LoadingState | undefined {
    const states = this.loadingStatesSignal();
    return states.get(key);
  }

  /**
   * 清除所有加载状态
   */
  clearAll(): void {
    this.updateStates(new Map());
  }

  /**
   * 获取所有正在加载的键
   */
  getLoadingKeys(): string[] {
    const states = this.loadingStatesSignal();
    return Array.from(states.values())
      .filter(state => state.loading)
      .map(state => state.key);
  }

  // 私有方法

  private updateStates(states: Map<string, LoadingState>): void {
    this.loadingStatesSignal.set(states);
    this.loadingStatesSubject.next(states);
  }
}
