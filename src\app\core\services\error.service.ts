import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ErrorState {
  /** 错误标识符 */
  key: string;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: string;
  /** 错误类型 */
  type?: 'error' | 'warning' | 'info';
  /** 错误时间 */
  timestamp: number;
  /** 是否已读 */
  read?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorService {
  // 错误状态管理
  private readonly errorsSubject = new BehaviorSubject<Map<string, ErrorState>>(new Map());
  private readonly errorsSignal = signal<Map<string, ErrorState>>(new Map());

  // 公开的只读状态
  readonly errors$ = this.errorsSubject.asObservable();
  readonly errors = this.errorsSignal.asReadonly();

  // 计算属性
  readonly hasErrors = computed(() => {
    const errors = this.errors();
    return errors.size > 0;
  });

  readonly unreadErrors = computed(() => {
    const errors = this.errors();
    return Array.from(errors.values()).filter(error => !error.read);
  });

  readonly latestError = computed(() => {
    const errors = this.errors();
    if (errors.size === 0) return null;
    
    return Array.from(errors.values())
      .sort((a, b) => b.timestamp - a.timestamp)[0];
  });

  /**
   * 添加错误
   */
  addError(key: string, message: string, details?: string, type: 'error' | 'warning' | 'info' = 'error'): void {
    const currentErrors = new Map(this.errorsSignal());
    currentErrors.set(key, {
      key,
      message,
      details,
      type,
      timestamp: Date.now(),
      read: false
    });
    
    this.updateErrors(currentErrors);
  }

  /**
   * 移除错误
   */
  removeError(key: string): void {
    const currentErrors = new Map(this.errorsSignal());
    currentErrors.delete(key);
    
    this.updateErrors(currentErrors);
  }

  /**
   * 标记错误为已读
   */
  markAsRead(key: string): void {
    const currentErrors = new Map(this.errorsSignal());
    const existingError = currentErrors.get(key);
    
    if (existingError) {
      currentErrors.set(key, {
        ...existingError,
        read: true
      });
      
      this.updateErrors(currentErrors);
    }
  }

  /**
   * 标记所有错误为已读
   */
  markAllAsRead(): void {
    const currentErrors = new Map(this.errorsSignal());
    
    for (const [key, error] of currentErrors.entries()) {
      currentErrors.set(key, {
        ...error,
        read: true
      });
    }
    
    this.updateErrors(currentErrors);
  }

  /**
   * 清除所有错误
   */
  clearAll(): void {
    this.updateErrors(new Map());
  }

  /**
   * 获取指定键的错误
   */
  getError(key: string): ErrorState | undefined {
    const errors = this.errorsSignal();
    return errors.get(key);
  }

  /**
   * 获取所有错误
   */
  getAllErrors(): ErrorState[] {
    const errors = this.errorsSignal();
    return Array.from(errors.values());
  }

  // 私有方法

  private updateErrors(errors: Map<string, ErrorState>): void {
    this.errorsSignal.set(errors);
    this.errorsSubject.next(errors);
  }
}
