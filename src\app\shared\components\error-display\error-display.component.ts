import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { ErrorService } from '../../../core/services/error.service';

@Component({
  selector: 'app-error-display',
  standalone: true,
  imports: [
    CommonModule,
    NzAlertModule,
    NzButtonModule,
    NzIconModule
  ],
  template: `
    <div class="error-container">
      @for (error of unreadErrors(); track error.key) {
        <nz-alert
          [nzType]="error.type || 'error'"
          [nzMessage]="error.message"
          [nzDescription]="error.details || ''"
          nzShowIcon
          nzClosable
          (nzOnClose)="onCloseError(error.key)"
          class="error-alert">
          
          @if (error.details) {
            <div nz-alert-action>
              <button 
                nz-button 
                nzSize="small" 
                nzType="text"
                (click)="toggleDetails(error.key)">
                <span nz-icon nzType="info-circle"></span>
                详情
              </button>
            </div>
          }
        </nz-alert>
      }
    </div>
  `,
  styles: [`
    .error-container {
      position: fixed;
      top: 24px;
      right: 24px;
      z-index: 1000;
      max-width: 400px;
      width: 100%;
    }
    
    .error-alert {
      margin-bottom: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    @media (max-width: 768px) {
      .error-container {
        left: 24px;
        right: 24px;
        max-width: none;
      }
    }
  `]
})
export class ErrorDisplayComponent {
  private readonly errorService = inject(ErrorService);

  // 计算属性
  readonly unreadErrors = computed(() => this.errorService.unreadErrors());

  onCloseError(key: string): void {
    this.errorService.removeError(key);
  }

  toggleDetails(key: string): void {
    // 这里可以实现详情展开/收起功能
    // 或者打开详情对话框
    console.log('Toggle details for error:', key);
  }
}
