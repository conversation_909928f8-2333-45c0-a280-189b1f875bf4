import { Injectable, inject, signal, computed } from '@angular/core';
import { Router, Route } from '@angular/router';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { MockApiService } from './mock-api.service';

import {
  DynamicRouteConfig,
  RouteLoadingState,
  LayoutType
} from '../types';
import {
  CACHE_CONFIG
} from '../constants';
import { getComponentLoader, isComponentRegistered } from '../constants/component-mapping';

/**
 * 动态路由服务
 *
 * 负责管理应用的动态路由配置，支持运行时添加、删除、更新路由。
 * 提供路由缓存、权限过滤和状态管理功能。
 *
 * @description
 * 核心功能：
 * - 动态路由的增删改查
 * - 路由配置的缓存和持久化
 * - 基于权限的路由过滤
 * - 路由加载状态管理
 * - 与 Angular Router 的集成
 *
 * 设计特点：
 * - 使用 Signal 和 Observable 双重状态管理
 * - 支持路由配置的本地缓存
 * - 提供类型安全的路由配置接口
 * - 支持懒加载组件的动态注册
 *
 * @example
 * ```typescript
 * // 基本使用
 * constructor(private dynamicRouteService: DynamicRouteService) {}
 *
 * // 添加新路由
 * const newRoute: DynamicRouteConfig = {
 *   id: 'new-feature',
 *   path: 'new-feature',
 *   title: '新功能',
 *   layout: LayoutType.DEFAULT,
 *   loadComponent: () => import('./new-feature.component').then(m => m.NewFeatureComponent)
 * };
 *
 * this.dynamicRouteService.addRoute(newRoute).subscribe(success => {
 *   if (success) {
 *     console.log('路由添加成功');
 *   }
 * });
 *
 * // 获取路由配置
 * const routes = this.dynamicRouteService.routeConfigs();
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class DynamicRouteService {
  /** 路由服务实例，用于操作 Angular 路由配置 */
  private readonly router = inject(Router);

  /** Mock API 服务实例，用于获取路由配置数据 */
  private readonly mockApi = inject(MockApiService);

  // ==================== 状态管理 ====================

  /**
   * 路由配置的 BehaviorSubject
   *
   * 管理动态路由配置列表的状态，支持订阅和状态变更通知。
   *
   * @private
   */
  private readonly routeConfigsSubject = new BehaviorSubject<DynamicRouteConfig[]>([]);

  /**
   * 加载状态的 BehaviorSubject
   *
   * 管理路由加载操作的状态，包括加载中、错误信息等。
   *
   * @private
   */
  private readonly loadingStateSubject = new BehaviorSubject<RouteLoadingState>({
    loading: false
  });

  /**
   * 路由配置的 Signal
   *
   * 使用 Angular Signal 提供现代化的响应式状态管理。
   *
   * @private
   */
  private readonly routeConfigsSignal = signal<DynamicRouteConfig[]>([]);

  /**
   * 加载状态的 Signal
   *
   * 使用 Signal 管理加载状态，提供更好的性能和类型安全。
   *
   * @private
   */
  private readonly loadingSignal = signal<RouteLoadingState>({ loading: false });

  // ==================== 公开状态接口 ====================

  /**
   * 路由配置的 Observable 流
   *
   * 提供路由配置变更的订阅接口。
   *
   * @readonly
   */
  readonly routeConfigs$ = this.routeConfigsSubject.asObservable();

  /**
   * 加载状态的 Observable 流
   *
   * 提供加载状态变更的订阅接口。
   *
   * @readonly
   */
  readonly loadingState$ = this.loadingStateSubject.asObservable();

  // ==================== 计算属性 ====================

  /**
   * 路由配置的 Signal（只读）
   *
   * 提供路由配置的 Signal 访问接口。
   *
   * @readonly
   */
  readonly routeConfigs = this.routeConfigsSignal.asReadonly();

  /**
   * 加载状态的 Signal（只读）
   *
   * 提供加载状态的 Signal 访问接口。
   *
   * @readonly
   */
  readonly loadingState = this.loadingSignal.asReadonly();

  /**
   * 是否正在加载的计算属性
   *
   * 基于加载状态自动计算是否正在加载。
   *
   * @readonly
   */
  readonly isLoading = computed(() => this.loadingState().loading);

  // ==================== 缓存管理 ====================

  /**
   * 缓存时间戳
   *
   * 记录路由配置缓存的创建时间，用于缓存有效性检查。
   * 当前暂未使用，保留以备后续功能扩展。
   *
   * @private
   */
  // private cacheTimestamp: number = 0; // 暂时不使用，保留以备后续功能扩展

  /**
   * 构造函数
   *
   * 初始化动态路由服务，自动加载缓存的路由配置。
   *
   * @description
   * 初始化流程：
   * 1. 从本地存储加载缓存的路由配置
   * 2. 验证缓存的有效性
   * 3. 如果缓存有效，恢复路由状态
   * 4. 如果缓存无效，保持空状态等待手动加载
   */
  constructor() {
    this.loadCachedRoutes();
  }

  /**
   * 从API加载路由配置
   *
   * @param parentPath 父路由路径，动态路由将添加到此路径下的 children 中
   * @returns Observable<DynamicRouteConfig[]> 加载的路由配置数组
   *
   * @description
   * 此方法从 API 加载动态路由配置，并将其添加到指定的父路由下。
   * 默认情况下，动态路由会添加到 '/view' 路由的 children 中。
   *
   * @example
   * ```typescript
   * // 将动态路由加载到 /view 路由下
   * this.dynamicRouteService.loadRoutesFromAPI('view').subscribe(routes => {
   *   console.log('动态路由加载成功:', routes);
   * });
   *
   * // 将动态路由加载到其他父路由下
   * this.dynamicRouteService.loadRoutesFromAPI('admin').subscribe(routes => {
   *   console.log('动态路由加载到 admin 路由下:', routes);
   * });
   * ```
   */
  loadRoutesFromAPI(parentPath: string = 'view'): Observable<DynamicRouteConfig[]> {
    this.setLoadingState({ loading: true });

    return this.mockApi.getRoutes().pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || '加载路由配置失败');
        }
        return response.data;
      }),
      tap(routes => {
        // 验证路由配置中的组件是否都已注册
        const validation = this.validateRouteComponents(routes);

        if (!validation.isValid) {
          console.warn('⚠️ 发现未注册的组件:', validation.missingComponents);
          console.warn('📝 请在 component-mapping.ts 中添加这些组件的映射配置');
        } else {
          console.log('✅ 所有组件都已正确注册:', validation.validComponents);
        }

        // 将动态路由添加到指定的父路由下
        this.addRoutesToParent(routes, parentPath);
        this.updateRouteConfigs(routes);
        this.cacheRoutes(routes);

        this.setLoadingState({ loading: false });
      }),
      catchError(error => {
        this.setLoadingState({
          loading: false,
          error: error.message || '加载路由配置失败'
        });
        return throwError(() => error);
      })
    );
  }

  /**
   * 动态添加路由
   *
   * @param routeConfig 路由配置
   * @param parentPath 父路由路径，如果指定则添加到父路由的 children 中
   * @returns Observable<boolean> 操作是否成功
   */
  addRoute(routeConfig: DynamicRouteConfig, parentPath?: string): Observable<boolean> {
    try {
      const route = this.convertToAngularRoute(routeConfig);

      if (parentPath) {
        // 添加到指定父路由的 children 中
        this.addRouteToParent(route, parentPath);
      } else {
        // 添加为顶级路由
        this.router.config.push(route);
        this.router.resetConfig(this.router.config);
      }

      // 更新本地状态
      const currentRoutes = this.routeConfigsSignal();
      const updatedRoutes = [...currentRoutes, routeConfig];
      this.updateRouteConfigs(updatedRoutes);
      this.cacheRoutes(updatedRoutes);

      return of(true);
    } catch (error) {
      return throwError(() => error);
    }
  }

  /**
   * 动态删除路由
   */
  removeRoute(routeId: string): Observable<boolean> {
    try {
      const currentRoutes = this.routeConfigsSignal();
      const routeToRemove = currentRoutes.find(r => r.id === routeId);

      if (!routeToRemove) {
        throw new Error(`路由 ${routeId} 不存在`);
      }

      // 从Angular路由配置中移除
      const routerConfig = this.router.config.filter(r =>
        r.path !== routeToRemove.path
      );
      this.router.resetConfig(routerConfig);

      // 更新本地状态
      const updatedRoutes = currentRoutes.filter(r => r.id !== routeId);
      this.updateRouteConfigs(updatedRoutes);
      this.cacheRoutes(updatedRoutes);

      return of(true);
    } catch (error) {
      return throwError(() => error);
    }
  }

  /**
   * 更新路由配置
   */
  updateRoute(routeConfig: DynamicRouteConfig): Observable<boolean> {
    try {
      const currentRoutes = this.routeConfigsSignal();
      const routeIndex = currentRoutes.findIndex(r => r.id === routeConfig.id);

      if (routeIndex === -1) {
        throw new Error(`路由 ${routeConfig.id} 不存在`);
      }

      // 更新Angular路由配置
      const route = this.convertToAngularRoute(routeConfig);
      const routerConfig = [...this.router.config];
      const angularRouteIndex = routerConfig.findIndex(r =>
        r.path === currentRoutes[routeIndex].path
      );

      if (angularRouteIndex !== -1) {
        routerConfig[angularRouteIndex] = route;
        this.router.resetConfig(routerConfig);
      }

      // 更新本地状态
      const updatedRoutes = [...currentRoutes];
      updatedRoutes[routeIndex] = routeConfig;
      this.updateRouteConfigs(updatedRoutes);
      this.cacheRoutes(updatedRoutes);

      return of(true);
    } catch (error) {
      return throwError(() => error);
    }
  }

  /**
   * 根据布局类型获取路由
   */
  getRoutesByLayout(layout: LayoutType): DynamicRouteConfig[] {
    return this.routeConfigsSignal().filter(route => route.layout === layout);
  }

  /**
   * 根据权限获取路由
   */
  getRoutesByPermissions(permissions: string[]): DynamicRouteConfig[] {
    return this.routeConfigsSignal().filter(route => {
      if (!route.permissions || route.permissions.length === 0) {
        return true;
      }
      return route.permissions.some(permission =>
        permissions.includes(permission)
      );
    });
  }

  /**
   * 获取单个路由配置
   */
  getRouteById(routeId: string): DynamicRouteConfig | undefined {
    return this.routeConfigsSignal().find(route => route.id === routeId);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    localStorage.removeItem(CACHE_CONFIG.ROUTE_CONFIG_KEY);
    // this.cacheTimestamp = 0; // 暂时不使用
  }

  /**
   * 重新加载路由配置
   */
  reloadRoutes(): Observable<DynamicRouteConfig[]> {
    this.clearCache();
    return this.loadRoutesFromAPI();
  }

  /**
   * 验证路由配置中的组件是否都已注册
   *
   * @param routes 路由配置数组
   * @returns 验证结果
   */
  validateRouteComponents(routes: DynamicRouteConfig[]): {
    isValid: boolean;
    missingComponents: string[];
    validComponents: string[];
  } {
    const missingComponents: string[] = [];
    const validComponents: string[] = [];

    const checkRoute = (route: DynamicRouteConfig) => {
      if (route.component && typeof route.component === 'string') {
        if (isComponentRegistered(route.component)) {
          validComponents.push(route.component);
        } else {
          missingComponents.push(route.component);
        }
      }

      // 递归检查子路由
      if (route.children) {
        route.children.forEach(checkRoute);
      }
    };

    routes.forEach(checkRoute);

    return {
      isValid: missingComponents.length === 0,
      missingComponents,
      validComponents
    };
  }

  // 私有方法

  /**
   * 将多个动态路由添加到指定父路由下
   *
   * @param routes 要添加的路由配置数组
   * @param parentPath 父路由路径
   *
   * @description
   * 此方法将一组动态路由配置添加到指定的父路由的 children 数组中。
   * 如果父路由不存在或没有 children 数组，会自动创建。
   */
  private addRoutesToParent(routes: DynamicRouteConfig[], parentPath: string): void {
    const routerConfig = [...this.router.config];
    const parentRoute = this.findRouteByPath(routerConfig, parentPath);

    if (!parentRoute) {
      console.warn(`父路由 "${parentPath}" 不存在，无法添加动态路由`);
      return;
    }

    // 确保父路由有 children 数组
    if (!parentRoute.children) {
      parentRoute.children = [];
    }

    // 将动态路由转换为 Angular 路由并添加到 children 中
    const angularRoutes = routes.map(route => this.convertToAngularRoute(route));
    parentRoute.children.push(...angularRoutes);

    // 重置路由配置
    this.router.resetConfig(routerConfig);

    console.log(`成功将 ${routes.length} 个动态路由添加到 "${parentPath}" 路由下`);
  }

  /**
   * 将单个路由添加到指定父路由下
   *
   * @param route Angular 路由配置
   * @param parentPath 父路由路径
   */
  private addRouteToParent(route: Route, parentPath: string): void {
    const routerConfig = [...this.router.config];
    const parentRoute = this.findRouteByPath(routerConfig, parentPath);

    if (!parentRoute) {
      throw new Error(`父路由 "${parentPath}" 不存在`);
    }

    // 确保父路由有 children 数组
    if (!parentRoute.children) {
      parentRoute.children = [];
    }

    // 添加路由到 children 中
    parentRoute.children.push(route);

    // 重置路由配置
    this.router.resetConfig(routerConfig);
  }

  /**
   * 根据路径查找路由配置
   *
   * @param routes 路由配置数组
   * @param path 要查找的路径
   * @returns 找到的路由配置或 undefined
   */
  private findRouteByPath(routes: Route[], path: string): Route | undefined {
    for (const route of routes) {
      if (route.path === path) {
        return route;
      }

      // 递归查找子路由
      if (route.children) {
        const found = this.findRouteByPath(route.children, path);
        if (found) {
          return found;
        }
      }
    }

    return undefined;
  }

  private updateRouteConfigs(routes: DynamicRouteConfig[]): void {
    this.routeConfigsSignal.set(routes);
    this.routeConfigsSubject.next(routes);
  }

  private setLoadingState(state: RouteLoadingState): void {
    this.loadingSignal.set(state);
    this.loadingStateSubject.next(state);
  }

  /**
   * 将动态路由配置转换为 Angular 路由配置
   *
   * @param routeConfig 动态路由配置
   * @returns Angular 路由配置
   *
   * @description
   * 支持多种动态组件加载方式：
   * 1. 约定式路径加载 - 根据路径自动推断组件位置
   * 2. 组件映射表加载 - 预定义的组件映射
   * 3. 远程模块加载 - 从远程服务器加载组件
   * 4. 默认组件加载 - 使用通用组件模板
   */
  private convertToAngularRoute(routeConfig: DynamicRouteConfig): Route {
    const route: Route = {
      path: routeConfig.path,
      data: {
        ...routeConfig.data,
        layout: routeConfig.layout,
        title: routeConfig.title,
        permissions: routeConfig.permissions,
        roles: routeConfig.roles,
        requireAuth: routeConfig.requireAuth
      }
    };

    // 处理组件配置
    if (routeConfig.component) {
      if (typeof routeConfig.component === 'string') {
        // 根据不同策略动态加载组件
        route.loadComponent = this.createDynamicComponentLoader(routeConfig);
      } else {
        // 直接传入的组件类
        route.component = routeConfig.component;
      }
    } else if (routeConfig.loadComponent) {
      // 直接传入的懒加载函数
      route.loadComponent = routeConfig.loadComponent;
    }

    // 处理重定向配置 - 注意：redirectTo 和 children 不能同时使用
    if (routeConfig.redirectTo && (!routeConfig.children || routeConfig.children.length === 0)) {
      route.redirectTo = routeConfig.redirectTo;
      route.pathMatch = routeConfig.pathMatch || 'full';
    }

    // 递归处理子路由
    if (routeConfig.children && routeConfig.children.length > 0) {
      route.children = routeConfig.children.map(child =>
        this.convertToAngularRoute(child)
      );
    }

    return route;
  }

  /**
   * 创建动态组件加载器
   *
   * @param routeConfig 路由配置
   * @returns 懒加载函数
   */
  private createDynamicComponentLoader(routeConfig: DynamicRouteConfig): () => Promise<any> {
    const componentName = routeConfig.component as string;

    return async () => {
      try {
        console.log(`🚀 策略1: 尝试从组件映射表加载组件: ${componentName}`);

        // 策略1: 尝试从组件映射表加载
        const mappedLoader = getComponentLoader(componentName);
        if (mappedLoader) {
          console.log(`📦 从映射表加载组件: ${componentName}`);
          return await mappedLoader();
        }

        console.log(`🔍 尝试从约定路径加载组件: ${componentName}`);

        // 策略2: 约定式路径加载
        const conventionComponent = await this.loadComponentByConvention(routeConfig);
        if (conventionComponent) {
          console.log(`🎯 约定式加载组件: ${componentName}`);
          return conventionComponent;
        }

        console.log(`🌐 尝试从远程加载组件: ${componentName}`);

        // 策略3: 远程模块加载
        const remoteComponent = await this.loadRemoteComponent(routeConfig);
        if (remoteComponent) {
          console.log(`🌐 远程加载组件: ${componentName}`);
          return remoteComponent;
        }

        // 策略4: 使用默认通用组件
        console.warn(`⚠️ 未找到组件 "${componentName}"，使用默认组件`);
        return await this.loadDefaultComponent(routeConfig);

      } catch (error) {
        console.error(`❌ 加载组件 "${componentName}" 失败:`, error);
        // 返回错误组件
        return await this.loadErrorComponent(routeConfig, error);
      }
    };
  }

  /**
   * 约定式路径加载组件
   * 根据路由路径自动推断组件位置
   */
  private async loadComponentByConvention(routeConfig: DynamicRouteConfig): Promise<any> {
    const componentName = routeConfig.component as string;
    const routePath = routeConfig.path;

    // 获取父路由路径（用于嵌套路由）
    const parentPath = this.getParentRoutePath(routeConfig);

    // 约定：组件路径 = pages/{父路径}/{组件名称}.component.ts
    // 从 src/app/core/services/ 到 src/app/pages/ 的正确相对路径
    const possiblePaths = [
      // 优先尝试父路径下的组件
      ...(parentPath ? [
        `../../../pages/${parentPath}/${this.kebabCase(componentName)}.component`,
        `../../../pages/${parentPath}/${routePath}.component`,
      ] : []),
      // 然后尝试当前路径下的组件
      `../../../pages/${routePath}/${this.kebabCase(componentName)}.component`,
      `../../../pages/${routePath}/${routePath}.component`,
      `../../../pages/${routePath}/index.component`,
      // 最后尝试直接在pages下查找
      `../../../pages/${this.kebabCase(componentName)}.component`,
    ];

    console.log(`尝试加载组件: ${componentName}`);
    console.log(`路由路径: ${routePath}, 父路径: ${parentPath || '无'}`);
    console.log(`可能的组件路径: ${possiblePaths.join(', ')}`);

    for (const path of possiblePaths) {
      try {
        console.log(`🔍 尝试约定式路径: ${path}`);
        const module = await import(path);
        const component = module[componentName] || module.default;
        if (component) {
          console.log(`✅ 成功加载组件: ${componentName} 从路径: ${path}`);
          return component;
        }
      } catch (error) {
        // 继续尝试下一个路径
        console.log(`❌ 路径失败: ${path}`);
        continue;
      }
    }

    return null;
  }

  /**
   * 远程模块加载
   * 从远程服务器加载组件模块
   */
  private async loadRemoteComponent(routeConfig: DynamicRouteConfig): Promise<any> {
    const componentName = routeConfig.component as string;

    // 检查是否配置了远程模块URL
    if (routeConfig.remoteModuleUrl) {
      try {
        console.log(`🌐 从远程加载: ${routeConfig.remoteModuleUrl}`);

        // 动态加载远程模块
        const module = await import(/* webpackIgnore: true */ routeConfig.remoteModuleUrl);
        return module[componentName] || module.default;
      } catch (error) {
        console.error('远程模块加载失败:', error);
      }
    }

    return null;
  }

  /**
   * 加载默认通用组件
   */
  private async loadDefaultComponent(_routeConfig: DynamicRouteConfig): Promise<any> {
    // 返回一个通用的动态组件
    return await import('../../shared/components/dynamic-page/dynamic-page.component')
      .then(m => m.DynamicPageComponent);
  }

  /**
   * 加载错误组件
   */
  private async loadErrorComponent(_routeConfig: DynamicRouteConfig, _error: any): Promise<any> {
    // 返回错误显示组件
    return await import('../../shared/components/component-error/component-error.component')
      .then(m => m.ComponentErrorComponent);
  }

  /**
   * 获取父路由路径
   * 用于嵌套路由的组件查找
   */
  private getParentRoutePath(routeConfig: DynamicRouteConfig): string | null {
    // 从路由ID中推断父路径
    // 例如：'products-list' -> 'products'
    const routeId = routeConfig.id;
    const parts = routeId.split('-');

    if (parts.length > 1) {
      // 返回第一部分作为父路径
      return parts[0];
    }

    return null;
  }

  /**
   * 将驼峰命名转换为短横线命名
   */
  private kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .toLowerCase()
      .replace(/component$/, '')
      .replace(/-$/, ''); // 移除末尾的连字符
  }

  private cacheRoutes(routes: DynamicRouteConfig[]): void {
    try {
      const cacheData = {
        routes,
        timestamp: Date.now()
      };
      localStorage.setItem(
        CACHE_CONFIG.ROUTE_CONFIG_KEY,
        JSON.stringify(cacheData)
      );
      // this.cacheTimestamp = cacheData.timestamp; // 暂时不使用
    } catch (error) {
      console.warn('缓存路由配置失败:', error);
    }
  }

  private loadCachedRoutes(): void {
    try {
      const cachedData = localStorage.getItem(CACHE_CONFIG.ROUTE_CONFIG_KEY);
      if (!cachedData) return;

      const { routes, timestamp } = JSON.parse(cachedData);
      const now = Date.now();

      // 检查缓存是否过期
      if (now - timestamp > CACHE_CONFIG.CACHE_EXPIRE_TIME) {
        this.clearCache();
        return;
      }

      this.updateRouteConfigs(routes);
      // this.cacheTimestamp = timestamp; // 暂时不使用
    } catch (error) {
      console.warn('加载缓存路由配置失败:', error);
      this.clearCache();
    }
  }
}
