import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzTableModule } from 'ng-zorro-antd/table';

/**
 * 订单统计组件（动态加载）
 * 
 * 这是一个动态加载的组件，用于演示动态路由功能
 * 显示订单相关的统计数据和分析图表
 */
@Component({
  selector: 'app-order-statistics',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzStatisticModule,
    NzGridModule,
    NzTagModule,
    NzProgressModule,
    NzTableModule
  ],
  template: `
    <div class="order-statistics-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="bar-chart" nzTheme="outline"></nz-icon>
          订单统计
        </h1>
        <p class="page-description">
          <nz-tag nzColor="processing">动态加载组件</nz-tag>
          <nz-tag nzColor="warning">管理员专用</nz-tag>
          查看订单数据统计和分析报告
        </p>
      </div>

      <!-- 核心指标 -->
      <nz-row [nzGutter]="[16, 16]" class="metrics-row">
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="今日订单"
              [nzValue]="todayOrders"
              nzSuffix="单"
              [nzValueStyle]="{ color: '#3f8600' }">
              <ng-template #nzPrefix>
                <nz-icon nzType="file-text"></nz-icon>
              </ng-template>
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="今日销售额"
              [nzValue]="todayRevenue"
              nzPrefix="¥"
              [nzValueStyle]="{ color: '#1890ff' }">
              <ng-template #nzPrefix>
                <nz-icon nzType="dollar"></nz-icon>
              </ng-template>
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="平均订单金额"
              [nzValue]="averageOrderValue"
              nzPrefix="¥"
              [nzValueStyle]="{ color: '#722ed1' }">
              <ng-template #nzPrefix>
                <nz-icon nzType="calculator"></nz-icon>
              </ng-template>
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="订单完成率"
              [nzValue]="completionRate"
              nzSuffix="%"
              [nzValueStyle]="{ color: '#eb2f96' }">
              <ng-template #nzPrefix>
                <nz-icon nzType="check-circle"></nz-icon>
              </ng-template>
            </nz-statistic>
          </nz-card>
        </nz-col>
      </nz-row>

      <!-- 订单状态分布 -->
      <nz-card nzTitle="订单状态分布" class="status-card">
        <nz-row [nzGutter]="16">
          <nz-col [nzSpan]="12">
            <div class="status-chart">
              <div *ngFor="let status of orderStatusData" class="status-item">
                <div class="status-info">
                  <span class="status-label">{{ status.label }}</span>
                  <span class="status-count">{{ status.count }} 单</span>
                </div>
                <nz-progress
                  [nzPercent]="status.percentage"
                  [nzStrokeColor]="status.color"
                  [nzShowInfo]="false">
                </nz-progress>
              </div>
            </div>
          </nz-col>
          <nz-col [nzSpan]="12">
            <div class="status-summary">
              <h4>状态说明</h4>
              <ul>
                <li><strong>待处理:</strong> 新下单，等待处理</li>
                <li><strong>处理中:</strong> 正在准备商品</li>
                <li><strong>已发货:</strong> 商品已发出</li>
                <li><strong>已送达:</strong> 订单完成</li>
                <li><strong>已取消:</strong> 订单被取消</li>
              </ul>
            </div>
          </nz-col>
        </nz-row>
      </nz-card>

      <!-- 热销产品排行 -->
      <nz-card nzTitle="热销产品排行" class="ranking-card">
        <nz-table #rankingTable [nzData]="topProductsData" [nzPageSize]="10" [nzShowPagination]="false">
          <thead>
            <tr>
              <th nzWidth="60px">排名</th>
              <th>产品名称</th>
              <th nzWidth="100px">销售数量</th>
              <th nzWidth="120px">销售金额</th>
              <th nzWidth="100px">占比</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let product of rankingTable.data; let i = index">
              <td>
                <div class="rank-badge" [class]="'rank-' + (i + 1)">
                  {{ i + 1 }}
                </div>
              </td>
              <td>{{ product.name }}</td>
              <td>{{ product.quantity }} 件</td>
              <td class="amount">¥{{ product.revenue | number:'1.2-2' }}</td>
              <td>
                <nz-progress
                  [nzPercent]="product.percentage"
                  nzSize="small"
                  [nzShowInfo]="true">
                </nz-progress>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 月度趋势 -->
      <nz-card nzTitle="月度订单趋势" class="trend-card">
        <div class="trend-content">
          <div class="trend-chart">
            <div class="chart-placeholder">
              <nz-icon nzType="line-chart" style="font-size: 48px; color: #d9d9d9;"></nz-icon>
              <p>图表区域 (可集成 ECharts 或其他图表库)</p>
            </div>
          </div>
          <div class="trend-summary">
            <h4>趋势分析</h4>
            <ul>
              <li>本月订单量较上月增长 <strong style="color: #52c41a;">+15.3%</strong></li>
              <li>销售额较上月增长 <strong style="color: #52c41a;">+22.8%</strong></li>
              <li>客单价较上月增长 <strong style="color: #52c41a;">+6.5%</strong></li>
              <li>订单完成率保持在 <strong style="color: #1890ff;">95%</strong> 以上</li>
            </ul>
          </div>
        </div>
      </nz-card>

      <!-- 功能说明 -->
      <nz-card nzTitle="功能说明" class="info-card">
        <div class="info-content">
          <h4>🔒 权限控制</h4>
          <p>此页面需要管理员权限才能访问，演示了基于权限的动态路由控制。</p>
          
          <h4>📊 统计功能</h4>
          <ul>
            <li>实时订单数据统计</li>
            <li>订单状态分布分析</li>
            <li>热销产品排行榜</li>
            <li>月度趋势分析</li>
          </ul>
        </div>
      </nz-card>
    </div>
  `,
  styles: [`
    .order-statistics-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .metrics-row {
      margin-bottom: 24px;
    }

    .status-card,
    .ranking-card,
    .trend-card {
      margin-bottom: 16px;
    }

    .status-chart {
      padding: 16px 0;
    }

    .status-item {
      margin-bottom: 16px;
    }

    .status-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .status-label {
      font-weight: 500;
      color: #262626;
    }

    .status-count {
      color: #8c8c8c;
    }

    .status-summary h4 {
      color: #262626;
      margin: 0 0 12px 0;
    }

    .status-summary ul {
      margin: 0;
      padding-left: 20px;
    }

    .status-summary li {
      margin: 8px 0;
      color: #595959;
    }

    .rank-badge {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 12px;
      color: white;
    }

    .rank-badge.rank-1 {
      background: #f5222d;
    }

    .rank-badge.rank-2 {
      background: #fa8c16;
    }

    .rank-badge.rank-3 {
      background: #fadb14;
    }

    .rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
      background: #d9d9d9;
      color: #595959;
    }

    .amount {
      font-weight: 600;
      color: #1890ff;
    }

    .trend-content {
      display: flex;
      gap: 24px;
    }

    .trend-chart {
      flex: 2;
    }

    .chart-placeholder {
      height: 300px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #8c8c8c;
    }

    .trend-summary {
      flex: 1;
    }

    .trend-summary h4 {
      color: #262626;
      margin: 0 0 12px 0;
    }

    .trend-summary ul {
      margin: 0;
      padding-left: 20px;
    }

    .trend-summary li {
      margin: 8px 0;
      color: #595959;
    }

    .info-card h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .info-card ul {
      margin: 8px 0 16px 20px;
    }

    .info-card li {
      margin: 4px 0;
      color: #595959;
    }

    @media (max-width: 768px) {
      .trend-content {
        flex-direction: column;
      }
    }
  `]
})
export class OrderStatisticsComponent {
  // 核心指标数据
  todayOrders = 45;
  todayRevenue = 125670;
  averageOrderValue = 2792.67;
  completionRate = 94.2;

  // 订单状态分布数据
  orderStatusData = [
    {
      label: '待处理',
      count: 12,
      percentage: 26.7,
      color: '#faad14'
    },
    {
      label: '处理中',
      count: 18,
      percentage: 40.0,
      color: '#1890ff'
    },
    {
      label: '已发货',
      count: 8,
      percentage: 17.8,
      color: '#722ed1'
    },
    {
      label: '已送达',
      count: 6,
      percentage: 13.3,
      color: '#52c41a'
    },
    {
      label: '已取消',
      count: 1,
      percentage: 2.2,
      color: '#ff4d4f'
    }
  ];

  // 热销产品数据
  topProductsData = [
    {
      name: '笔记本电脑',
      quantity: 25,
      revenue: 149975,
      percentage: 35.2
    },
    {
      name: '智能手机',
      quantity: 42,
      revenue: 138558,
    },
    {
      name: '办公椅',
      quantity: 18,
      revenue: 16182,
      percentage: 3.8
    },
    {
      name: '蓝牙耳机',
      quantity: 35,
      revenue: 10465,
      percentage: 2.5
    },
    {
      name: '咖啡机',
      quantity: 8,
      revenue: 10392,
      percentage: 2.4
    }
  ];
}
