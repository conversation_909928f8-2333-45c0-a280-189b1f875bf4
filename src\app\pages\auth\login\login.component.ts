import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';

import { AuthService } from '../../../core/services';

/**
 * 登录组件
 *
 * 提供用户登录功能的独立组件，包含表单验证、加载状态管理和错误处理。
 * 支持演示账户快速登录功能。
 *
 * @description
 * 主要功能：
 * - 响应式表单验证
 * - 用户名密码登录
 * - 记住我功能
 * - 演示账户快速登录
 * - 基于角色的登录后重定向
 * - 友好的错误提示和加载状态
 *
 * 设计特点：
 * - 使用 Angular Standalone Component
 * - 集成 NG-ZORRO UI 组件
 * - 响应式表单验证
 * - 现代化的依赖注入模式
 * - 内联模板和样式
 *
 * @example
 * ```typescript
 * // 在路由中使用
 * {
 *   path: 'login',
 *   component: LoginComponent,
 *   data: { title: '用户登录' }
 * }
 *
 * // 在模板中使用
 * <app-login></app-login>
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCheckboxModule,
    NzIconModule
  ],
  template: `
    <div class="login-container">
      <div class="login-header">
        <h2>用户登录</h2>
        <p>欢迎回来，请输入您的账户信息</p>
      </div>
      
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <nz-form-item>
          <nz-form-control nzErrorTip="请输入用户名">
            <nz-input-group nzPrefixIcon="user">
              <input 
                type="text" 
                nz-input 
                formControlName="username" 
                placeholder="用户名"
                autocomplete="username">
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control nzErrorTip="请输入密码">
            <nz-input-group nzPrefixIcon="lock">
              <input 
                type="password" 
                nz-input 
                formControlName="password" 
                placeholder="密码"
                autocomplete="current-password">
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control>
            <div class="login-options">
              <label nz-checkbox formControlName="remember">记住我</label>
              <a class="forgot-password">忘记密码？</a>
            </div>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control>
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              nzBlock
              [nzLoading]="loading"
              [disabled]="!loginForm.valid">
              登录
            </button>
          </nz-form-control>
        </nz-form-item>
      </form>
      
      <div class="login-footer">
        <p>还没有账户？ <a>立即注册</a></p>
      </div>
      
      <!-- 演示账户 -->
      <div class="demo-accounts">
        <h4>演示账户</h4>
        <div class="demo-account-list">
          <div class="demo-account" (click)="loginAsDemo('admin')">
            <strong>管理员账户</strong>
            <span>admin / admin123</span>
          </div>
          <div class="demo-account" (click)="loginAsDemo('user')">
            <strong>普通用户</strong>
            <span>user / user123</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      width: 100%;
      max-width: 400px;
      margin: 0 auto;
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 32px;
    }
    
    .login-header h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }
    
    .login-header p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
    
    .login-form {
      margin-bottom: 24px;
    }
    
    .login-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .forgot-password {
      color: #1890ff;
      text-decoration: none;
    }
    
    .forgot-password:hover {
      text-decoration: underline;
    }
    
    .login-footer {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
    }
    
    .login-footer p {
      margin: 0;
      color: #8c8c8c;
    }
    
    .login-footer a {
      color: #1890ff;
      text-decoration: none;
    }
    
    .login-footer a:hover {
      text-decoration: underline;
    }
    
    .demo-accounts {
      margin-top: 32px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 6px;
    }
    
    .demo-accounts h4 {
      margin: 0 0 12px 0;
      color: #595959;
      font-size: 14px;
      text-align: center;
    }
    
    .demo-account-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .demo-account {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .demo-account:hover {
      background: #e6f7ff;
      border-color: #1890ff;
    }
    
    .demo-account strong {
      color: #262626;
      font-size: 13px;
    }
    
    .demo-account span {
      color: #8c8c8c;
      font-size: 12px;
      font-family: monospace;
    }
  `]
})
export class LoginComponent {
  // ==================== 依赖注入 ====================

  /** 表单构建器，用于创建响应式表单 */
  private readonly fb = inject(FormBuilder);

  /** 认证服务，用于处理登录逻辑 */
  private readonly authService = inject(AuthService);

  /** 路由服务，用于登录成功后的页面跳转 */
  private readonly router = inject(Router);

  /** 消息服务，用于显示成功或错误提示 */
  private readonly message = inject(NzMessageService);

  // ==================== 组件状态 ====================

  /**
   * 加载状态标志
   *
   * 控制登录按钮的加载状态和禁用状态，防止重复提交。
   *
   * @type {boolean}
   */
  loading = false;

  /**
   * 登录表单
   *
   * 使用 Angular 响应式表单构建的登录表单，包含用户名、密码和记住我选项。
   *
   * @type {FormGroup}
   *
   * @description
   * 表单字段：
   * - username: 用户名（必填）
   * - password: 密码（必填）
   * - remember: 记住我（可选，布尔值）
   *
   * 验证规则：
   * - 用户名和密码都是必填项
   * - 使用 Angular 内置验证器
   */
  loginForm: FormGroup = this.fb.group({
    username: ['', [Validators.required]],
    password: ['', [Validators.required]],
    remember: [false]
  });

  // ==================== 核心方法 ====================

  /**
   * 处理表单提交
   *
   * 验证表单数据，调用认证服务进行登录，处理登录结果。
   *
   * @returns void
   *
   * @example
   * ```typescript
   * // 在模板中绑定
   * <form (ngSubmit)="onSubmit()">
   *   <!-- 表单内容 -->
   * </form>
   *
   * // 手动调用
   * this.onSubmit();
   * ```
   *
   * @description
   * 提交流程：
   * 1. 检查表单验证状态
   * 2. 如果验证通过，设置加载状态
   * 3. 提取用户名和密码
   * 4. 调用认证服务进行登录
   * 5. 处理登录成功：显示成功消息，根据角色重定向
   * 6. 处理登录失败：显示错误消息，重置加载状态
   * 7. 如果表单验证失败，标记所有无效字段为脏状态
   */
  onSubmit(): void {
    if (this.loginForm.valid) {
      // 设置加载状态，禁用表单和按钮
      this.loading = true;

      // 从表单中提取登录凭据
      const { username, password } = this.loginForm.value;

      // 调用认证服务进行登录
      this.authService.login({ username, password }).subscribe({
        next: (userInfo) => {
          // 登录成功处理
          this.loading = false;
          this.message.success('登录成功');

          // 根据用户角色进行智能重定向
          // 管理员用户重定向到管理后台
          this.router.navigate(['/view/dashboard']);
        },
        error: (error) => {
          // 登录失败处理
          this.loading = false;
          // 显示友好的错误消息
          this.message.error(error.message || '登录失败');
        }
      });
    } else {
      // 表单验证失败，标记所有无效字段为脏状态以显示错误提示
      Object.values(this.loginForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  /**
   * 演示账户快速登录
   *
   * 提供预设的演示账户，方便开发和测试使用。
   *
   * @param type - 演示账户类型，'admin' 为管理员账户，'user' 为普通用户账户
   * @returns void
   *
   * @example
   * ```typescript
   * // 在模板中使用
   * <button (click)="loginAsDemo('admin')">管理员登录</button>
   * <button (click)="loginAsDemo('user')">用户登录</button>
   *
   * // 在组件中调用
   * this.loginAsDemo('admin'); // 使用管理员账户登录
   * ```
   *
   * @description
   * 演示账户信息：
   * - admin: 用户名 'admin'，密码 'admin123'，拥有管理员权限
   * - user: 用户名 'user'，密码 'user123'，拥有普通用户权限
   *
   * 此功能主要用于：
   * - 开发阶段的快速测试
   * - 演示应用功能
   * - 新用户的快速体验
   */
  loginAsDemo(type: 'admin' | 'user'): void {
    // 预设的演示账户凭据
    const credentials = {
      admin: { username: 'admin', password: 'admin123' },
      user: { username: 'user', password: 'user123' }
    };

    // 将演示账户信息填充到表单中
    this.loginForm.patchValue(credentials[type]);

    // 自动提交表单
    this.onSubmit();
  }
}
