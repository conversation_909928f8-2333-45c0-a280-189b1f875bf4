import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

import {
  DynamicRouteConfig,
  RouteConfigResponse,
  UserInfo,
  LayoutType,
  Permission
} from '../types';
import { DEFAULT_PERMISSIONS, DEFAULT_ROLES } from '../constants';

/**
 * 模拟API服务，用于开发和演示
 * 在实际项目中，这些方法应该调用真实的后端API
 */
@Injectable({
  providedIn: 'root'
})
export class MockApiService {
  // 模拟用户数据
  private readonly mockUsers: Record<string, UserInfo> = {
    'admin': {
      id: '1',
      username: 'admin',
      displayName: '管理员',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '1',
          name: DEFAULT_ROLES.ADMIN,
          permissions: this.getAdminPermissions()
        }
      ],
      permissions: this.getAdminPermissions().map(p => p.code),
      authenticated: true
    },
    'user': {
      id: '2',
      username: 'user',
      displayName: '普通用户',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '2',
          name: DEFAULT_ROLES.USER,
          permissions: this.getUserPermissions()
        }
      ],
      permissions: this.getUserPermissions().map(p => p.code),
      authenticated: true
    }
  };

  // 模拟动态路由配置 - 这些路由将被动态添加到 /view 路由的 children 中
  // 注意：这里的 component 字段使用字符串，模拟后台 API 返回的真实数据格式
  private readonly mockRoutes: DynamicRouteConfig[] = [
    // 动态功能模块 - 产品管理（已有组件）
    {
      id: 'dynamic-products',
      path: 'products',
      title: '产品管理',
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
      permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
      menu: {
        id: 'dynamic-products',
        title: '产品管理',
        icon: 'shopping',
        path: '/view/products'
      },
      children: [
        {
          id: 'products-index',
          path: '',
          title: '产品管理',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
          permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
          redirectTo: 'list',
          pathMatch: 'full'
        },
        {
          id: 'products-list',
          path: 'list',
          title: '产品列表',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
          permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
          // 后台返回的组件名称字符串（已有组件）
          component: 'ProductListComponent',
          menu: {
            id: 'products-list',
            title: '产品列表',
            icon: 'unordered-list',
            path: '/view/products/list'
          }
        },
        {
          id: 'products-categories',
          path: 'categories',
          title: '产品分类',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN],
          permissions: [DEFAULT_PERMISSIONS.SYSTEM_ADMIN],
          // 后台返回的组件名称字符串（已有组件）
          component: 'ProductCategoriesComponent',
          menu: {
            id: 'products-categories',
            title: '产品分类',
            icon: 'appstore',
            path: '/view/products/categories'
          }
        }
      ]
    },
  ];

  /**
   * 模拟登录
   */
  login(credentials: { username: string; password: string }): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        const { username, password } = credentials;

        // 检查用户名和密码
        if (username === 'admin' && password === 'admin123') {
          observer.next(this.mockUsers['admin']);
          observer.complete();
        } else if (username === 'user' && password === 'user123') {
          observer.next(this.mockUsers['user']);
          observer.complete();
        } else {
          observer.error(new Error('用户名或密码错误'));
        }
      }, 1000);
    });
  }

  /**
   * 模拟获取路由配置
   */
  getRoutes(): Observable<RouteConfigResponse> {
    // 模拟网络延迟
    return of({
      success: true,
      data: this.mockRoutes,
      total: this.mockRoutes.length
    }).pipe(delay(1800));
  }

  /**
   * 模拟获取用户信息
   */
  getUserInfo(userId?: string): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        // 在实际项目中，这里应该根据用户ID或令牌获取用户信息
        const user = userId
          ? (this.mockUsers[userId] || null)
          : this.mockUsers['admin']; // 默认返回管理员用户

        if (user) {
          observer.next(user);
          observer.complete();
        } else {
          observer.error(new Error('用户不存在'));
        }
      }, 800);
    });
  }

  // 私有方法

  private getAdminPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.SYSTEM_ADMIN, name: '系统管理权限' },
      { code: DEFAULT_PERMISSIONS.SYSTEM_CONFIG, name: '系统配置权限' },
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_CREATE, name: '创建用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_UPDATE, name: '更新用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_DELETE, name: '删除用户权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_VIEW, name: '查看角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_CREATE, name: '创建角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_UPDATE, name: '更新角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_DELETE, name: '删除角色权限' },
      { code: DEFAULT_PERMISSIONS.MENU_VIEW, name: '查看菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_CREATE, name: '创建菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_UPDATE, name: '更新菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_DELETE, name: '删除菜单权限' }
    ];
  }

  private getUserPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' }
    ];
  }
}
