# 动态路由加载指南

## 概述

本指南详细介绍了如何使用 `DynamicRouteService` 的 `loadRoutesFromAPI` 方法将动态路由加载到现有的路由结构中。

## 核心功能

### `loadRoutesFromAPI` 方法

```typescript
loadRoutesFromAPI(parentPath: string = 'view'): Observable<DynamicRouteConfig[]>
```

**参数说明：**
- `parentPath`: 父路由路径，动态路由将添加到此路径下的 `children` 中
- 默认值为 `'view'`，即将动态路由添加到 `/view` 路由下

**返回值：**
- `Observable<DynamicRouteConfig[]>`: 加载的路由配置数组

## 使用方法

### 1. 基本使用

```typescript
import { DynamicRouteService } from './core/services/dynamic-route.service';

@Component({
  // ...
})
export class MyComponent {
  constructor(private dynamicRouteService: DynamicRouteService) {}

  loadDynamicRoutes(): void {
    // 将动态路由加载到 /view 路由下（默认）
    this.dynamicRouteService.loadRoutesFromAPI().subscribe({
      next: (routes) => {
        console.log('动态路由加载成功:', routes);
      },
      error: (error) => {
        console.error('动态路由加载失败:', error);
      }
    });
  }
}
```

### 2. 指定父路由

```typescript
// 将动态路由加载到 /admin 路由下
this.dynamicRouteService.loadRoutesFromAPI('admin').subscribe(routes => {
  console.log('路由已添加到 /admin 下:', routes);
});

// 将动态路由加载到 /user 路由下
this.dynamicRouteService.loadRoutesFromAPI('user').subscribe(routes => {
  console.log('路由已添加到 /user 下:', routes);
});
```

### 3. 完整示例

```typescript
import { Component, inject, signal } from '@angular/core';
import { DynamicRouteService } from './core/services/dynamic-route.service';

@Component({
  selector: 'app-route-manager',
  template: `
    <button 
      (click)="loadRoutes()" 
      [disabled]="isLoading()">
      {{ isLoading() ? '加载中...' : '加载动态路由' }}
    </button>
    
    <div *ngIf="routes().length > 0">
      <h3>已加载的路由：</h3>
      <ul>
        <li *ngFor="let route of routes()">
          {{ route.title }} ({{ route.path }})
        </li>
      </ul>
    </div>
  `
})
export class RouteManagerComponent {
  private readonly dynamicRouteService = inject(DynamicRouteService);
  
  readonly isLoading = this.dynamicRouteService.isLoading;
  readonly routes = this.dynamicRouteService.routeConfigs;

  loadRoutes(): void {
    this.dynamicRouteService.loadRoutesFromAPI('view').subscribe({
      next: (routes) => {
        console.log(`成功加载 ${routes.length} 个动态路由`);
      },
      error: (error) => {
        console.error('加载失败:', error);
      }
    });
  }
}
```

## 路由结构示例

### 加载前的路由结构

```
/view (DefaultLayoutComponent)
├── dashboard
├── reports
│   ├── sales
│   └── users
└── settings
    ├── users
    └── roles
```

### 加载后的路由结构

```
/view (DefaultLayoutComponent)
├── dashboard (现有)
├── reports (现有)
│   ├── sales
│   └── users
├── settings (现有)
│   ├── users
│   └── roles
├── products (动态加载) ← 新增
│   ├── list
│   └── categories
├── orders (动态加载) ← 新增
│   ├── list
│   └── statistics
├── customers (动态加载) ← 新增
└── marketing (动态加载) ← 新增
    ├── campaigns
    └── coupons
```

## API 数据格式

### 路由配置数据结构

```typescript
interface DynamicRouteConfig {
  id: string;                    // 路由唯一标识
  path: string;                  // 路由路径
  title: string;                 // 路由标题
  layout: LayoutType;            // 布局类型
  requireAuth: boolean;          // 是否需要认证
  roles?: string[];              // 允许的角色
  permissions?: string[];        // 需要的权限
  menu?: MenuConfig;             // 菜单配置
  children?: DynamicRouteConfig[]; // 子路由
  component?: any;               // 组件
  loadComponent?: () => Promise<any>; // 懒加载组件
}
```

### API 响应格式

```typescript
interface RouteConfigResponse {
  success: boolean;
  data: DynamicRouteConfig[];
  total: number;
  message?: string;
}
```

## 技术实现原理

### 1. 路由查找

系统使用 `findRouteByPath` 方法在现有路由配置中查找指定的父路由：

```typescript
private findRouteByPath(routes: Route[], path: string): Route | undefined {
  for (const route of routes) {
    if (route.path === path) {
      return route;
    }
    
    // 递归查找子路由
    if (route.children) {
      const found = this.findRouteByPath(route.children, path);
      if (found) return found;
    }
  }
  return undefined;
}
```

### 2. 路由添加

找到父路由后，将动态路由转换为 Angular 路由格式并添加到 `children` 数组中：

```typescript
private addRoutesToParent(routes: DynamicRouteConfig[], parentPath: string): void {
  const routerConfig = [...this.router.config];
  const parentRoute = this.findRouteByPath(routerConfig, parentPath);
  
  if (!parentRoute) {
    console.warn(`父路由 "${parentPath}" 不存在`);
    return;
  }

  // 确保父路由有 children 数组
  if (!parentRoute.children) {
    parentRoute.children = [];
  }

  // 转换并添加动态路由
  const angularRoutes = routes.map(route => this.convertToAngularRoute(route));
  parentRoute.children.push(...angularRoutes);

  // 重置路由配置
  this.router.resetConfig(routerConfig);
}
```

### 3. 路由转换

`convertToAngularRoute` 方法将 `DynamicRouteConfig` 转换为 Angular 的 `Route` 格式：

```typescript
private convertToAngularRoute(routeConfig: DynamicRouteConfig): Route {
  const route: Route = {
    path: routeConfig.path,
    data: {
      layout: routeConfig.layout,
      title: routeConfig.title,
      permissions: routeConfig.permissions,
      roles: routeConfig.roles,
      requireAuth: routeConfig.requireAuth
    }
  };

  // 设置组件
  if (routeConfig.component) {
    route.component = routeConfig.component;
  } else if (routeConfig.loadComponent) {
    route.loadComponent = routeConfig.loadComponent;
  }

  // 处理子路由
  if (routeConfig.children?.length > 0) {
    route.children = routeConfig.children.map(child => 
      this.convertToAngularRoute(child)
    );
  }

  return route;
}
```

## 缓存机制

### 自动缓存

动态路由加载成功后会自动缓存到 localStorage：

```typescript
private cacheRoutes(routes: DynamicRouteConfig[]): void {
  const cacheData = {
    routes,
    timestamp: Date.now()
  };
  localStorage.setItem(CACHE_CONFIG.ROUTE_CONFIG_KEY, JSON.stringify(cacheData));
}
```

### 缓存恢复

应用启动时会自动尝试从缓存恢复路由配置：

```typescript
private loadCachedRoutes(): void {
  const cachedData = localStorage.getItem(CACHE_CONFIG.ROUTE_CONFIG_KEY);
  if (cachedData) {
    const { routes, timestamp } = JSON.parse(cachedData);
    
    // 检查缓存是否过期（默认30分钟）
    if (Date.now() - timestamp < CACHE_CONFIG.CACHE_EXPIRE_TIME) {
      this.updateRouteConfigs(routes);
    }
  }
}
```

## 实际应用场景

### 1. 模块化应用

```typescript
// 在应用初始化时加载核心模块路由
ngOnInit(): void {
  this.loadCoreModules();
}

private loadCoreModules(): void {
  this.dynamicRouteService.loadRoutesFromAPI('view').subscribe({
    next: (routes) => {
      console.log('核心模块路由加载完成');
      this.loadUserSpecificModules();
    }
  });
}

private loadUserSpecificModules(): void {
  // 根据用户权限加载特定模块
  const userRole = this.authService.getCurrentUserRole();
  if (userRole === 'admin') {
    this.loadAdminModules();
  }
}
```

### 2. 插件系统

```typescript
// 动态加载插件路由
loadPlugin(pluginId: string): void {
  this.pluginService.getPluginRoutes(pluginId).subscribe(routes => {
    // 将插件路由添加到指定位置
    routes.forEach(route => {
      this.dynamicRouteService.addRoute(route, 'view').subscribe();
    });
  });
}
```

### 3. 权限控制

```typescript
// 根据用户权限动态加载路由
loadUserRoutes(): void {
  const userPermissions = this.authService.getUserPermissions();
  
  this.dynamicRouteService.loadRoutesFromAPI('view').subscribe(routes => {
    // 过滤用户有权限的路由
    const filteredRoutes = this.filterRoutesByPermissions(routes, userPermissions);
    console.log('用户可访问的路由:', filteredRoutes);
  });
}
```

## 最佳实践

### 1. 错误处理

```typescript
loadRoutes(): void {
  this.dynamicRouteService.loadRoutesFromAPI('view').subscribe({
    next: (routes) => {
      this.message.success(`成功加载 ${routes.length} 个路由`);
    },
    error: (error) => {
      this.message.error('路由加载失败，请稍后重试');
      console.error('路由加载错误:', error);
    }
  });
}
```

### 2. 加载状态管理

```typescript
@Component({
  template: `
    <nz-spin [nzSpinning]="isLoading()">
      <button (click)="loadRoutes()">加载路由</button>
    </nz-spin>
  `
})
export class RouteLoaderComponent {
  readonly isLoading = this.dynamicRouteService.isLoading;
  
  loadRoutes(): void {
    // 加载状态会自动管理
    this.dynamicRouteService.loadRoutesFromAPI().subscribe();
  }
}
```

### 3. 路由验证

```typescript
loadRoutes(): void {
  this.dynamicRouteService.loadRoutesFromAPI('view').subscribe({
    next: (routes) => {
      // 验证路由配置
      const validRoutes = routes.filter(route => this.validateRoute(route));
      console.log(`加载了 ${validRoutes.length} 个有效路由`);
    }
  });
}

private validateRoute(route: DynamicRouteConfig): boolean {
  return !!(route.id && route.path && route.title);
}
```

## 演示页面

系统提供了一个完整的演示页面来展示动态路由加载功能：

**访问路径**: `/view/admin/dynamic-routes`

**功能特性**:
- 从 API 加载动态路由到 /view 下
- 查看当前路由配置
- 重新加载路由
- 清除缓存
- 实时状态显示

这个演示页面是学习和测试动态路由功能的最佳起点。

---

通过以上指南，您可以完全掌握如何使用 `loadRoutesFromAPI` 方法将动态路由加载到现有的路由结构中，实现灵活的路由管理和模块化应用架构。
