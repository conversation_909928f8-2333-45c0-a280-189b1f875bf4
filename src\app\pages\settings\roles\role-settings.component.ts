import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';

/**
 * 角色管理组件
 * 
 * 管理系统角色的增删改查功能
 * 包含角色列表、添加角色、编辑角色、删除角色、权限配置等功能
 */
@Component({
  selector: 'app-role-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzFormModule,
    NzInputModule,
    NzModalModule,
    NzPopconfirmModule,
    NzTagModule,
    NzCheckboxModule
  ],
  template: `
    <div class="role-settings-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="team" nzTheme="outline"></nz-icon>
          角色管理
        </h1>
        <p class="page-description">管理系统角色和权限，包括添加、编辑、删除角色等操作</p>
      </div>

      <!-- 角色管理表格 -->
      <nz-card nzTitle="角色列表" class="role-table-card">
        <div class="table-actions">
          <button nz-button nzType="primary" (click)="showAddRoleModal()">
            <nz-icon nzType="plus"></nz-icon>
            添加角色
          </button>
          <button nz-button>
            <nz-icon nzType="reload"></nz-icon>
            刷新
          </button>
        </div>
        
        <nz-table #basicTable [nzData]="roleList" [nzPageSize]="10">
          <thead>
            <tr>
              <th>角色名称</th>
              <th>角色描述</th>
              <th>权限数量</th>
              <th>用户数量</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let role of basicTable.data">
              <td>
                <nz-tag [nzColor]="getRoleColor(role.code)">{{ role.name }}</nz-tag>
              </td>
              <td>{{ role.description }}</td>
              <td>{{ role.permissions.length }} 个权限</td>
              <td>{{ role.userCount }} 个用户</td>
              <td>{{ role.createTime }}</td>
              <td>
                <button nz-button nzType="link" nzSize="small" (click)="editRole(role)">
                  <nz-icon nzType="edit"></nz-icon>
                  编辑
                </button>
                <button nz-button nzType="link" nzSize="small" (click)="configPermissions(role)">
                  <nz-icon nzType="setting"></nz-icon>
                  权限配置
                </button>
                <button 
                  nz-button 
                  nzType="link" 
                  nzSize="small" 
                  nzDanger
                  nz-popconfirm
                  nzPopconfirmTitle="确定要删除这个角色吗？"
                  (nzOnConfirm)="deleteRole(role.id)"
                  [disabled]="role.code === 'admin' || role.code === 'user'">
                  <nz-icon nzType="delete"></nz-icon>
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 添加/编辑角色模态框 -->
      <nz-modal
        [(nzVisible)]="isRoleModalVisible"
        [nzTitle]="roleModalTitle"
        (nzOnCancel)="handleRoleCancel()"
        (nzOnOk)="handleRoleOk()"
        [nzOkLoading]="isLoading">
        <ng-container *nzModalContent>
          <form nz-form [formGroup]="roleForm" [nzLayout]="'vertical'">
            <nz-form-item>
              <nz-form-label nzRequired>角色名称</nz-form-label>
              <nz-form-control nzErrorTip="请输入角色名称">
                <input nz-input formControlName="name" placeholder="请输入角色名称" />
              </nz-form-control>
            </nz-form-item>
            
            <nz-form-item>
              <nz-form-label nzRequired>角色代码</nz-form-label>
              <nz-form-control nzErrorTip="请输入角色代码">
                <input nz-input formControlName="code" placeholder="请输入角色代码（英文）" />
              </nz-form-control>
            </nz-form-item>
            
            <nz-form-item>
              <nz-form-label>角色描述</nz-form-label>
              <nz-form-control>
                <textarea nz-input formControlName="description" placeholder="请输入角色描述" rows="3"></textarea>
              </nz-form-control>
            </nz-form-item>
          </form>
        </ng-container>
      </nz-modal>

      <!-- 权限配置模态框 -->
      <nz-modal
        [(nzVisible)]="isPermissionModalVisible"
        nzTitle="权限配置"
        (nzOnCancel)="handlePermissionCancel()"
        (nzOnOk)="handlePermissionOk()"
        [nzOkLoading]="isLoading"
        nzWidth="600px">
        <ng-container *nzModalContent>
          <div class="permission-config">
            <h4>为角色 "{{ currentRole?.name }}" 配置权限</h4>
            <div class="permission-groups">
              <div *ngFor="let group of permissionGroups" class="permission-group">
                <h5>{{ group.name }}</h5>
                <div class="permission-items">
                  <label 
                    *ngFor="let permission of group.permissions" 
                    nz-checkbox 
                    [(ngModel)]="permission.checked"
                    class="permission-item">
                    {{ permission.name }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </nz-modal>
    </div>
  `,
  styles: [`
    .role-settings-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .role-table-card {
      margin-top: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .permission-config h4 {
      margin-bottom: 16px;
      color: #262626;
    }

    .permission-groups {
      max-height: 400px;
      overflow-y: auto;
    }

    .permission-group {
      margin-bottom: 20px;
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }

    .permission-group h5 {
      margin: 0 0 12px 0;
      color: #1890ff;
      font-weight: 600;
    }

    .permission-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .permission-item {
      margin: 0;
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #fafafa;
    }

    .permission-item:hover {
      background: #e6f7ff;
      border-color: #91d5ff;
    }
  `]
})
export class RoleSettingsComponent {
  isRoleModalVisible = false;
  isPermissionModalVisible = false;
  isLoading = false;
  roleModalTitle = '添加角色';
  currentRoleId: string | null = null;
  currentRole: any = null;

  roleForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.roleForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      description: ['']
    });
  }

  /**
   * 角色列表模拟数据
   */
  roleList = [
    {
      id: '1',
      name: '管理员',
      code: 'admin',
      description: '系统管理员，拥有所有权限',
      permissions: ['user:view', 'user:edit', 'user:delete', 'system:admin'],
      userCount: 2,
      createTime: '2023-12-01 10:00:00'
    },
    {
      id: '2',
      name: '普通用户',
      code: 'user',
      description: '普通用户，拥有基本权限',
      permissions: ['user:view'],
      userCount: 15,
      createTime: '2023-12-01 10:00:00'
    },
    {
      id: '3',
      name: '编辑者',
      code: 'editor',
      description: '内容编辑者，可以编辑内容',
      permissions: ['user:view', 'content:edit'],
      userCount: 5,
      createTime: '2024-01-10 14:30:00'
    }
  ];

  /**
   * 权限组模拟数据
   */
  permissionGroups = [
    {
      name: '用户管理',
      permissions: [
        { code: 'user:view', name: '查看用户', checked: false },
        { code: 'user:edit', name: '编辑用户', checked: false },
        { code: 'user:delete', name: '删除用户', checked: false }
      ]
    },
    {
      name: '内容管理',
      permissions: [
        { code: 'content:view', name: '查看内容', checked: false },
        { code: 'content:edit', name: '编辑内容', checked: false },
        { code: 'content:delete', name: '删除内容', checked: false }
      ]
    },
    {
      name: '系统管理',
      permissions: [
        { code: 'system:admin', name: '系统管理', checked: false },
        { code: 'system:config', name: '系统配置', checked: false },
        { code: 'system:log', name: '查看日志', checked: false }
      ]
    }
  ];

  /**
   * 显示添加角色模态框
   */
  showAddRoleModal(): void {
    this.roleModalTitle = '添加角色';
    this.currentRoleId = null;
    this.roleForm.reset();
    this.isRoleModalVisible = true;
  }

  /**
   * 编辑角色
   */
  editRole(role: any): void {
    this.roleModalTitle = '编辑角色';
    this.currentRoleId = role.id;
    this.roleForm.patchValue({
      name: role.name,
      code: role.code,
      description: role.description
    });
    this.isRoleModalVisible = true;
  }

  /**
   * 配置权限
   */
  configPermissions(role: any): void {
    this.currentRole = role;

    // 重置权限选择状态
    this.permissionGroups.forEach(group => {
      group.permissions.forEach(permission => {
        permission.checked = role.permissions.includes(permission.code);
      });
    });

    this.isPermissionModalVisible = true;
  }

  /**
   * 删除角色
   */
  deleteRole(roleId: string): void {
    this.roleList = this.roleList.filter(role => role.id !== roleId);
    this.message.success('角色删除成功');
  }

  /**
   * 角色模态框确认
   */
  handleRoleOk(): void {
    if (this.roleForm.valid) {
      this.isLoading = true;

      setTimeout(() => {
        const formValue = this.roleForm.value;

        if (this.currentRoleId) {
          // 编辑角色
          const roleIndex = this.roleList.findIndex(role => role.id === this.currentRoleId);
          if (roleIndex > -1) {
            this.roleList[roleIndex] = {
              ...this.roleList[roleIndex],
              ...formValue
            };
          }
          this.message.success('角色更新成功');
        } else {
          // 添加角色
          const newRole = {
            id: Date.now().toString(),
            ...formValue,
            permissions: [],
            userCount: 0,
            createTime: new Date().toLocaleString()
          };
          this.roleList = [...this.roleList, newRole];
          this.message.success('角色添加成功');
        }

        this.isLoading = false;
        this.isRoleModalVisible = false;
        this.roleForm.reset();
      }, 1000);
    } else {
      Object.values(this.roleForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  /**
   * 角色模态框取消
   */
  handleRoleCancel(): void {
    this.isRoleModalVisible = false;
    this.roleForm.reset();
  }

  /**
   * 权限配置确认
   */
  handlePermissionOk(): void {
    this.isLoading = true;

    setTimeout(() => {
      // 收集选中的权限
      const selectedPermissions: string[] = [];
      this.permissionGroups.forEach(group => {
        group.permissions.forEach(permission => {
          if (permission.checked) {
            selectedPermissions.push(permission.code);
          }
        });
      });

      // 更新角色权限
      const roleIndex = this.roleList.findIndex(role => role.id === this.currentRole.id);
      if (roleIndex > -1) {
        this.roleList[roleIndex].permissions = selectedPermissions;
      }

      this.message.success('权限配置成功');
      this.isLoading = false;
      this.isPermissionModalVisible = false;
    }, 1000);
  }

  /**
   * 权限配置取消
   */
  handlePermissionCancel(): void {
    this.isPermissionModalVisible = false;
  }

  /**
   * 根据角色代码获取标签颜色
   */
  getRoleColor(code: string): string {
    switch (code) {
      case 'admin':
        return 'red';
      case 'user':
        return 'blue';
      case 'editor':
        return 'green';
      default:
        return 'default';
    }
  }
}
