<style>
    .default-layout {
        min-height: 100vh;
    }

    .default-header {
        background: #0D759F;
        border-bottom: 3px solid transparent;
        border-image: linear-gradient(270deg, #45A2CD 0%, #9AC258 100%) 1;
        display: flex;
        align-items: center;
        padding: 0;
        color: white;
    }

    .logo-image {
        margin-left: 24px;
        margin-right: 12px;
        cursor: pointer;
    }

    .logo {
        flex: none;
        line-height: 63px;
        font-size: 28px;
        font-weight: bold;
        width: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .header-right-container {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        padding: 0 18px;
    }

    .header-right {
        display: flex;
        align-items: center;
        /* 间隔 8 px */
        gap: 8px;
    }

    .user-info {
        padding: 0 12px;
        border-radius: 5px;
        background: #f5f5f5;
        color: #333;
        font-size: 14px;
        line-height: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }


    .default-sider {
        background: #fff;
    }

    .default-content {
        height: calc(100vh - 64px);
        overflow: auto;
    }

    .breadcrumb-container {
        padding: 3px 8px;
    }

    .button-white {
        color: white;
    }

    @media (max-width: 767px) {
        .responsive-logo {
            display: none;
        }
    }
</style>

<nz-layout class="default-layout">
    <nz-header class="default-header">
        <!-- Logo -->
        <img src="assets/logo.svg" class="logo-image responsive-logo" alt="Logo" height="32">
        <div class="logo">
            Trademark
        </div>
        <div class="header-right-container">
            <div class="header-right">
            </div>
            <div class="header-right">
                <!-- 用户信息 -->
                <div class="user-info" nz-dropdown nzPlacement="bottomCenter" nzTrigger="click" [nzDropdownMenu]="menu">
                    {{ userInfo()?.username }}
                </div>
                <nz-dropdown-menu #menu="nzDropdownMenu">
                    <ul nz-menu>
                        <li nz-menu-item>1st menu item</li>
                        <li nz-menu-item>2nd menu item</li>
                        <li nz-menu-divider></li>
                        <li nz-menu-item>2nd menu item</li>
                    </ul>
                </nz-dropdown-menu>
                <!-- 退出按钮 -->
                <button class="button-white" nz-button nzType="text" (click)="logout()" title="退出登录">
                    <nz-icon nzType="logout" nzTheme="outline" />
                </button>
            </div>
        </div>
    </nz-header>
    <nz-layout has-sider>
        <!-- 侧边栏 -->
        <nz-sider nzCollapsible (nzCollapsedChange)="onCollapsedChange($event)" class="default-sider" nzTheme="light"
            [nzCollapsedWidth]="isVisible ? 64: 0" [nzCollapsed]="isCollapsed()">
            <!-- 菜单 -->
            <ul nz-menu nzMode="inline" [nzInlineCollapsed]="isCollapsed()">
                <ng-container *ngFor="let item of dynamicMenuItems()">
                    <!-- 如果有子菜单 -->
                    <li nz-submenu *ngIf="item.children && item.children.length > 0" [nzTitle]="item.title!"
                        [nzIcon]="item.icon || ''">
                        <ul>
                            <ng-container *ngFor="let child of item.children">
                                <!-- 如果子菜单还有子菜单 (支持多级) -->
                                <li nz-submenu *ngIf="child.children && child.children.length > 0"
                                    [nzTitle]="child.title!" [nzIcon]="child.icon || ''">
                                    <ul>
                                        <li nz-menu-item *ngFor="let grandChild of child.children"
                                            [routerLink]="grandChild.path!">
                                            <nz-icon *ngIf="grandChild.icon" [nzType]="grandChild.icon!"></nz-icon>
                                            <span>{{ grandChild.title }}</span>
                                        </li>
                                    </ul>
                                </li>
                                <!-- 如果是普通子菜单项 -->
                                <li nz-menu-item *ngIf="!child.children || child.children.length === 0"
                                    [routerLink]="child.path!">
                                    <nz-icon *ngIf="child.icon" [nzType]="child.icon!"></nz-icon>
                                    <span>{{ child.title }}</span>
                                </li>
                            </ng-container>
                        </ul>
                    </li>
                    <!-- 如果是普通菜单项 (没有子菜单) -->
                    <li nz-menu-item *ngIf="!item.children || item.children.length === 0" [routerLink]="item.path!">
                        <nz-icon *ngIf="item.icon" [nzType]="item.icon!"></nz-icon>
                        <span>{{ item.title }}</span>
                    </li>
                </ng-container>
            </ul>

        </nz-sider>
        <!-- 内容 -->
        <nz-content class="default-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-container">
                <nz-breadcrumb>
                    <nz-breadcrumb-item>Home</nz-breadcrumb-item>
                    <nz-breadcrumb-item>
                        <a>Application List</a>
                    </nz-breadcrumb-item>
                    <nz-breadcrumb-item>An Application</nz-breadcrumb-item>
                </nz-breadcrumb>
            </div>
            <!-- 内容区域 -->
            <div class="content-wrapper">
                <router-outlet></router-outlet>
            </div>
        </nz-content>
    </nz-layout>
</nz-layout>