import { Injectable, inject } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';

import { AuthService } from '../services/auth.service';
import { DEFAULT_ROUTES } from '../constants';

/**
 * 权限守卫服务
 *
 * 负责检查用户是否具有访问特定路由所需的权限或角色。
 * 实现了基于角色的访问控制（RBAC）和细粒度的权限管理。
 *
 * @description
 * 权限守卫的工作原理：
 * 1. 从路由配置中获取所需的权限和角色
 * 2. 检查用户是否已认证
 * 3. 验证用户是否具有所需的权限或角色
 * 4. 根据检查结果允许访问或重定向到错误页面
 *
 * 支持两种权限控制方式：
 * - permissions: 细粒度的功能权限（如 'user:create', 'report:view'）
 * - roles: 基于角色的权限（如 'admin', 'manager', 'user'）
 *
 * @example
 * ```typescript
 * // 基于权限的路由保护
 * {
 *   path: 'users',
 *   component: UserListComponent,
 *   canActivate: [AuthGuard, PermissionGuard],
 *   data: {
 *     permissions: ['user:view', 'user:list']
 *   }
 * }
 *
 * // 基于角色的路由保护
 * {
 *   path: 'admin',
 *   component: AdminComponent,
 *   canActivate: [AuthGuard, PermissionGuard],
 *   data: {
 *     roles: ['admin', 'super_admin']
 *   }
 * }
 *
 * // 同时使用权限和角色
 * {
 *   path: 'sensitive-data',
 *   component: SensitiveDataComponent,
 *   canActivate: [AuthGuard, PermissionGuard],
 *   data: {
 *     permissions: ['data:view'],
 *     roles: ['admin', 'data_analyst']
 *   }
 * }
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class PermissionGuard implements CanActivate, CanActivateChild {
  /** 认证服务实例，用于检查用户权限和角色 */
  private readonly authService = inject(AuthService);

  /** 路由服务实例，用于权限不足时的重定向 */
  private readonly router = inject(Router);

  /**
   * 路由激活权限检查
   *
   * 在路由激活之前检查用户是否具有所需的权限或角色。
   *
   * @param route - 当前激活的路由快照，包含权限配置数据
   * @param state - 当前路由状态快照，用于重定向时保存URL
   * @returns Observable<boolean> - 返回是否允许激活路由的Observable
   *
   * @example
   * ```typescript
   * // Angular 会自动调用此方法
   * // 路由配置示例：
   * {
   *   path: 'protected',
   *   component: ProtectedComponent,
   *   canActivate: [PermissionGuard],
   *   data: {
   *     permissions: ['feature:access'],
   *     roles: ['user']
   *   }
   * }
   * ```
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermissions(route, state);
  }

  /**
   * 子路由激活权限检查
   *
   * 在子路由激活之前检查用户权限，用于保护嵌套路由结构。
   *
   * @param childRoute - 子路由快照，包含子路由的权限配置
   * @param state - 当前路由状态快照
   * @returns Observable<boolean> - 返回是否允许激活子路由的Observable
   *
   * @example
   * ```typescript
   * // 保护所有子路由
   * {
   *   path: 'admin',
   *   component: AdminLayoutComponent,
   *   canActivateChild: [PermissionGuard],
   *   data: { roles: ['admin'] },
   *   children: [
   *     {
   *       path: 'users',
   *       component: UsersComponent,
   *       data: { permissions: ['user:manage'] }
   *     }
   *   ]
   * }
   * ```
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermissions(childRoute, state);
  }

  /**
   * 核心权限检查逻辑
   *
   * 执行详细的权限验证，包括：
   * 1. 解析路由配置中的权限要求
   * 2. 验证用户认证状态
   * 3. 检查用户权限和角色
   * 4. 处理权限不足的情况
   *
   * @private
   * @param route - 路由快照，用于获取权限配置
   * @param state - 路由状态快照，用于重定向
   * @returns Observable<boolean> - 权限检查结果
   *
   * @description
   * 权限检查的详细逻辑：
   * 1. 从路由数据中提取 permissions 和 roles 配置
   * 2. 如果没有配置任何权限要求，直接允许访问
   * 3. 确保用户已认证（未认证则重定向到登录页）
   * 4. 使用 "任一匹配" 策略检查权限和角色
   * 5. 只有同时满足权限和角色要求才允许访问
   * 6. 权限不足时重定向到 403 禁止访问页面
   */
  private checkPermissions(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // 从路由配置中获取所需的权限和角色
    // permissions: 功能级权限数组，如 ['user:create', 'user:edit']
    const requiredPermissions = route.data?.['permissions'] as string[] || [];
    // roles: 角色级权限数组，如 ['admin', 'manager']
    const requiredRoles = route.data?.['roles'] as string[] || [];

    // 如果路由没有配置任何权限要求，直接允许访问
    // 这适用于公开页面或只需要认证但不需要特定权限的页面
    if (requiredPermissions.length === 0 && requiredRoles.length === 0) {
      return of(true);
    }

    // 确保用户已认证
    // 权限检查的前提是用户必须已经登录
    if (!this.authService.isAuthenticated()) {
      // 未认证用户重定向到登录页，并保存当前URL用于登录后返回
      this.router.navigate([DEFAULT_ROUTES.LOGIN], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    }

    // 检查用户权限
    // 使用 "任一匹配" 策略：用户只需要拥有所需权限中的任意一个
    // 如果没有配置权限要求（数组为空），则权限检查通过
    const hasRequiredPermissions = requiredPermissions.length === 0 ||
      this.authService.hasAnyPermission(requiredPermissions);

    // 检查用户角色
    // 使用 "任一匹配" 策略：用户只需要拥有所需角色中的任意一个
    // 如果没有配置角色要求（数组为空），则角色检查通过
    const hasRequiredRoles = requiredRoles.length === 0 ||
      this.authService.hasAnyRole(requiredRoles);

    // 同时满足权限和角色要求才允许访问
    // 这实现了 AND 逻辑：权限 AND 角色都必须满足
    if (hasRequiredPermissions && hasRequiredRoles) {
      return of(true);
    } else {
      // 权限不足，重定向到403禁止访问页面
      // 不保存返回URL，因为用户即使重新登录也可能没有权限
      this.router.navigate([DEFAULT_ROUTES.FORBIDDEN]);
      return of(false);
    }
  }
}
