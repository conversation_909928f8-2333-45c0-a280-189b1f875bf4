# Angular 企业级动态路由系统

基于 Angular 20 和 NG-ZORRO 构建的现代化企业级动态路由管理系统，支持统一后台布局、权限控制、动态路由配置等功能。

> **文档说明**: 除本 README.md 文件外，所有其他项目文档都统一存放在 [docs](./docs) 文件夹中。

## 🚀 特性

- ✅ **动态路由管理** - 支持运行时动态添加/删除路由
- ✅ **统一后台布局** - admin 和 user 角色使用相同的管理界面布局
- ✅ **权限控制** - 基于角色和权限的路由守卫
- ✅ **智能根路径重定向** - 根据认证状态和角色自动跳转
- ✅ **类型安全** - 完整的 TypeScript 类型定义
- ✅ **响应式设计** - 支持移动端和桌面端
- ✅ **错误处理** - 全局错误处理和用户友好的错误提示
- ✅ **加载状态** - 优雅的加载状态管理
- ✅ **缓存机制** - 路由配置和用户信息缓存
- ✅ **Mock API** - 内置模拟 API 服务，便于开发和演示

## 📦 技术栈

- **Angular 20** - 最新的 Angular 框架
- **NG-ZORRO 20** - 企业级 UI 组件库
- **TypeScript 5.8** - 类型安全的 JavaScript
- **RxJS 7.8** - 响应式编程
- **Angular Signals** - 新一代响应式状态管理
- **Standalone Components** - Angular 独立组件

## 🏗️ 项目结构

```
src/app/
├── core/                    # 核心模块
│   ├── constants/          # 常量定义
│   ├── guards/             # 路由守卫 (AuthGuard, PermissionGuard, SmartRedirectGuard)
│   ├── services/           # 核心服务 (AuthService, DynamicRouteService, LayoutService)
│   └── types/              # 类型定义
├── layouts/                # 布局组件
│   ├── default/            # 默认后台管理布局 (admin 和 user 共用)
│   ├── auth/               # 认证布局 (登录页面)
│   └── blank/              # 空白布局 (特殊页面)
├── pages/                  # 页面组件
│   ├── dashboard/          # 仪表盘页面 (统一的后台首页)
│   ├── auth/               # 认证页面 (登录)
│   └── error/              # 错误页面 (404, 403)
└── shared/                 # 共享组件
    └── components/         # 通用组件 (Loading, ErrorDisplay)
```

## 📋 重要说明

### 布局设计理念

本项目采用**统一后台布局**的设计理念：

- **admin 和 user 角色** 都使用相同的后台管理界面布局
- 两种角色都是后台管理系统的操作人员，不需要特殊的布局区分
- 统一的布局提供一致的用户体验和更简单的维护

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

### 3. 访问应用

打开浏览器访问 `http://localhost:4200`

### 4. 演示账户

- **管理员账户**: `admin` / `admin123`
- **普通用户**: `user` / `user123`

### 5. 主要功能演示

1. 访问根路径 "/"，系统将根据认证状态智能重定向
2. 登录后自动跳转到 `/view/dashboard`
3. 体验统一的后台管理布局
4. 测试不同角色的权限控制

## 📚 文档索引

所有项目文档都统一存放在 [docs](./docs) 文件夹中，包括：

### 核心文档

- [项目概述](./docs/PROJECT_SUMMARY.md) - 项目架构和核心功能概览
- [文档总索引](./docs/COMPREHENSIVE_DOCUMENTATION_INDEX.md) - 完整文档目录和使用指南

### 功能文档

- [认证系统文档](./docs/AUTHENTICATION_DOCUMENTATION.md) - 认证流程和用户管理
- [动态路由文档](./docs/DYNAMIC_ROUTING_DOCUMENTATION.md) - 动态路由管理系统
- [路由守卫文档](./docs/ROUTE_GUARDS_DOCUMENTATION.md) - 权限控制和路由保护
- [布局系统文档](./docs/LAYOUT_DOCUMENTATION.md) - 多布局系统和响应式设计
- [加载状态文档](./docs/LOADING_STATE_DOCUMENTATION.md) - 加载状态管理

### 实用文档

- [实用示例文档](./docs/PRACTICAL_EXAMPLES_DOCUMENTATION.md) - 完整功能示例
- [使用示例文档](./docs/USAGE_EXAMPLES.md) - 常见使用场景示例
- [代码注释总结](./docs/CODE_COMMENTS_SUMMARY.md) - 中文代码注释汇总

## 📖 核心功能

### 🔄 动态路由管理

系统支持运行时动态添加、删除和更新路由配置：

```typescript
// 添加动态路由
const routeConfig: DynamicRouteConfig = {
  id: 'new-route',
  path: 'new-page',
  title: '新页面',
  layout: LayoutType.DEFAULT,
  requireAuth: true,
  permissions: ['admin:view']
};

this.dynamicRouteService.addRoute(routeConfig).subscribe();
```

### 🎨 统一布局系统

采用统一的后台管理布局设计：

- **默认后台布局** - admin 和 user 角色共用的管理界面布局
- **认证布局** - 登录注册等认证页面的专用布局
- **空白布局** - 最小化布局，适用于特殊页面
- **响应式设计** - 完美适配桌面端和移动端

### 🔐 权限控制系统

基于角色和权限的细粒度访问控制：

```typescript
// 路由级权限控制
{
  path: 'view',
  canActivate: [AuthGuard, PermissionGuard],
  data: {
    requireAuth: true,
    roles: ['admin', 'user'],
    permissions: ['dashboard:view']
  }
}
```

### 🛡️ 智能重定向

根据用户认证状态和角色自动重定向：

- 未认证用户自动跳转到登录页
- 已认证用户根据角色跳转到相应页面
- 支持登录后返回原访问页面

## 🛠️ 开发指南

### 添加新页面

1. 创建页面组件：

```typescript
@Component({
  selector: 'app-new-page',
  standalone: true,
  imports: [CommonModule, NzCardModule],
  template: `
    <nz-card nzTitle="新页面">
      <p>页面内容</p>
    </nz-card>
  `
})
export class NewPageComponent {}
```

2. 配置路由：

```typescript
const routeConfig: DynamicRouteConfig = {
  id: 'new-page',
  path: 'new-page',
  title: '新页面',
  layout: LayoutType.DEFAULT,
  component: NewPageComponent,
  requireAuth: true,
  roles: ['admin', 'user']
};

this.dynamicRouteService.addRoute(routeConfig).subscribe();
```

### 权限控制

1. 路由级权限控制：

```typescript
// 在路由配置中设置权限
{
  path: 'sensitive-data',
  component: SensitiveDataComponent,
  canActivate: [AuthGuard, PermissionGuard],
  data: {
    requireAuth: true,
    roles: ['admin'],
    permissions: ['data:view']
  }
}
```

2. 组件级权限控制：

```typescript
// 在组件模板中使用权限检查
<div *ngIf="authService.hasPermission('user:edit')">
  <button (click)="editUser()">编辑用户</button>
</div>

<div *ngIf="authService.hasRole('admin')">
  <button (click)="adminAction()">管理员操作</button>
</div>
```

## 🔧 配置说明

### 环境配置

项目支持多环境配置，可在 `src/environments/` 目录下配置不同环境的参数。

### API 配置

当前使用 Mock API 服务，生产环境中需要替换为真实的后端 API：

```typescript
// 在 mock-api.service.ts 中配置模拟数据
// 生产环境中替换为 HttpClient 调用真实 API
```

### 主题配置

在 `app.config.ts` 中配置 NG-ZORRO 主题：

```typescript
provideNzConfig({
  theme: {
    primaryColor: '#1890ff'
  }
})
```

## 🧪 测试

```bash
# 运行单元测试
npm test

# 运行端到端测试
npm run e2e
```

## 📦 构建部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run build && npm run serve
```

## 🎯 最佳实践

1. **遵循 Angular 风格指南** - 使用官方推荐的代码规范
2. **使用 TypeScript 类型** - 充分利用类型安全特性
3. **组件化开发** - 使用 Standalone Components 架构
4. **响应式编程** - 合理使用 RxJS 和 Angular Signals
5. **权限控制** - 在路由和组件级别实施权限检查
6. **性能优化** - 使用懒加载和 OnPush 变更检测策略

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- [Angular](https://angular.io/) - 强大的前端框架
- [NG-ZORRO](https://ng.ant.design/) - 企业级 UI 组件库
- [Ant Design](https://ant.design/) - 优秀的设计语言

---

**Happy Coding! 🎉**

> 💡 **提示**: 如需了解更多详细信息，请查看 [docs](./docs) 文件夹中的完整文档。
