import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzStatisticModule,
    NzGridModule,
    NzIconModule,
    NzTableModule,
    NzTagModule
  ],
  template: `
    <div class="dashboard">
      <h1>管理员仪表盘</h1>
      
    </div>
  `,
  styles: [`
    .dashboard {
      padding: 0;
    }
    
    .dashboard h1 {
      margin-bottom: 24px;
      color: #262626;
    }
    
    .stats-row {
      margin-bottom: 24px;
    }
    
    .content-row {
      margin-bottom: 24px;
    }
    
    .system-info {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .info-item:last-child {
      border-bottom: none;
    }
    
    .label {
      font-weight: 500;
      color: #595959;
    }
  `]
})
export class DashboardComponent implements OnInit {

  recentOrders = [
    {
      orderNo: 'ORD001',
      customer: '张三',
      amount: 1299,
      status: '已完成',
      time: '2024-01-10 14:30'
    },
    {
      orderNo: 'ORD002',
      customer: '李四',
      amount: 899,
      status: '处理中',
      time: '2024-01-10 13:15'
    },
    {
      orderNo: 'ORD003',
      customer: '王五',
      amount: 2199,
      status: '已发货',
      time: '2024-01-10 11:45'
    },
    {
      orderNo: 'ORD004',
      customer: '赵六',
      amount: 599,
      status: '待付款',
      time: '2024-01-10 10:20'
    }
  ];

  ngOnInit(): void {
    // 初始化数据
  }

  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      '已完成': 'green',
      '处理中': 'blue',
      '已发货': 'orange',
      '待付款': 'red'
    };
    return colorMap[status] || 'default';
  }
}
