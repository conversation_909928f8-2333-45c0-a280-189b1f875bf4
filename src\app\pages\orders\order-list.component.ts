import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

/**
 * 订单列表组件（动态加载）
 * 
 * 这是一个动态加载的组件，用于演示动态路由功能
 * 显示订单列表和订单管理功能
 */
@Component({
  selector: 'app-order-list',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    NzInputModule,
    NzSelectModule,
    NzDatePickerModule
  ],
  template: `
    <div class="order-list-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="file-text" nzTheme="outline"></nz-icon>
          订单列表
        </h1>
        <p class="page-description">
          <nz-tag nzColor="processing">动态加载组件</nz-tag>
          查看和管理所有订单信息
        </p>
      </div>

      <!-- 搜索和筛选 -->
      <nz-card class="filter-card">
        <div class="filter-row">
          <div class="filter-item">
            <label>订单号:</label>
            <input nz-input placeholder="请输入订单号" style="width: 200px;" />
          </div>
          <div class="filter-item">
            <label>订单状态:</label>
            <nz-select nzPlaceHolder="请选择状态" style="width: 150px;">
              <nz-option nzValue="all" nzLabel="全部"></nz-option>
              <nz-option nzValue="pending" nzLabel="待处理"></nz-option>
              <nz-option nzValue="processing" nzLabel="处理中"></nz-option>
              <nz-option nzValue="shipped" nzLabel="已发货"></nz-option>
              <nz-option nzValue="delivered" nzLabel="已送达"></nz-option>
              <nz-option nzValue="cancelled" nzLabel="已取消"></nz-option>
            </nz-select>
          </div>
          <div class="filter-item">
            <label>日期范围:</label>
            <nz-range-picker style="width: 240px;"></nz-range-picker>
          </div>
          <div class="filter-item">
            <button nz-button nzType="primary">
              <nz-icon nzType="search"></nz-icon>
              搜索
            </button>
            <button nz-button>
              <nz-icon nzType="reload"></nz-icon>
              重置
            </button>
          </div>
        </div>
      </nz-card>

      <!-- 订单表格 -->
      <nz-card nzTitle="订单信息" class="table-card">
        <div class="table-actions">
          <button nz-button nzType="primary">
            <nz-icon nzType="plus"></nz-icon>
            新建订单
          </button>
          <button nz-button>
            <nz-icon nzType="download"></nz-icon>
            导出订单
          </button>
          <button nz-button>
            <nz-icon nzType="sync"></nz-icon>
            批量处理
          </button>
        </div>

        <nz-table #orderTable [nzData]="orderData" [nzPageSize]="10" [nzScroll]="{ x: '1200px' }">
          <thead>
            <tr>
              <th nzWidth="120px">订单号</th>
              <th nzWidth="100px">客户</th>
              <th nzWidth="200px">商品</th>
              <th nzWidth="100px">数量</th>
              <th nzWidth="120px">金额</th>
              <th nzWidth="100px">状态</th>
              <th nzWidth="120px">下单时间</th>
              <th nzWidth="120px">更新时间</th>
              <th nzWidth="160px" nzRight>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let order of orderTable.data">
              <td>{{ order.orderNo }}</td>
              <td>{{ order.customerName }}</td>
              <td>
                <div class="product-info">
                  <div class="product-name">{{ order.productName }}</div>
                  <div class="product-spec">{{ order.productSpec }}</div>
                </div>
              </td>
              <td>{{ order.quantity }}</td>
              <td class="amount">¥{{ order.amount | number:'1.2-2' }}</td>
              <td>
                <nz-tag [nzColor]="getStatusColor(order.status)">
                  {{ order.statusText }}
                </nz-tag>
              </td>
              <td>{{ order.createTime }}</td>
              <td>{{ order.updateTime }}</td>
              <td nzRight>
                <button nz-button nzType="link" nzSize="small">
                  <nz-icon nzType="eye"></nz-icon>
                  查看
                </button>
                <button nz-button nzType="link" nzSize="small" [disabled]="order.status === 'delivered'">
                  <nz-icon nzType="edit"></nz-icon>
                  编辑
                </button>
                <button nz-button nzType="link" nzSize="small" nzDanger [disabled]="order.status === 'delivered'">
                  <nz-icon nzType="delete"></nz-icon>
                  取消
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 订单统计 -->
      <nz-card nzTitle="订单统计" class="stats-card">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon pending">
              <nz-icon nzType="clock-circle"></nz-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOrderCountByStatus('pending') }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon processing">
              <nz-icon nzType="loading"></nz-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOrderCountByStatus('processing') }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon shipped">
              <nz-icon nzType="car"></nz-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOrderCountByStatus('shipped') }}</div>
              <div class="stat-label">已发货</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon delivered">
              <nz-icon nzType="check-circle"></nz-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOrderCountByStatus('delivered') }}</div>
              <div class="stat-label">已送达</div>
            </div>
          </div>
        </div>
      </nz-card>
    </div>
  `,
  styles: [`
    .order-list-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .filter-card {
      margin-bottom: 16px;
    }

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
    }

    .filter-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .filter-item label {
      font-weight: 500;
      color: #262626;
      white-space: nowrap;
    }

    .filter-item button {
      margin-left: 8px;
    }

    .table-card {
      margin-bottom: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .product-info {
      line-height: 1.4;
    }

    .product-name {
      font-weight: 500;
      color: #262626;
    }

    .product-spec {
      font-size: 12px;
      color: #8c8c8c;
    }

    .amount {
      font-weight: 600;
      color: #1890ff;
    }

    .stats-card {
      margin-bottom: 16px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fafafa;
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 18px;
      color: white;
    }

    .stat-icon.pending {
      background: #faad14;
    }

    .stat-icon.processing {
      background: #1890ff;
    }

    .stat-icon.shipped {
      background: #722ed1;
    }

    .stat-icon.delivered {
      background: #52c41a;
    }

    .stat-content {
      flex: 1;
    }

    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 4px;
    }

    .stat-label {
      color: #8c8c8c;
      font-size: 12px;
    }
  `]
})
export class OrderListComponent {
  /**
   * 订单数据模拟
   */
  orderData = [
    {
      orderNo: 'ORD202401001',
      customerName: '张三',
      productName: '笔记本电脑',
      productSpec: 'Intel i7 16GB 512GB SSD',
      quantity: 1,
      amount: 5999.00,
      status: 'pending',
      statusText: '待处理',
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      orderNo: 'ORD202401002',
      customerName: '李四',
      productName: '智能手机',
      productSpec: '128GB 蓝色',
      quantity: 2,
      amount: 6598.00,
      status: 'processing',
      statusText: '处理中',
      createTime: '2024-01-15 09:15:00',
      updateTime: '2024-01-15 11:20:00'
    },
    {
      orderNo: 'ORD202401003',
      customerName: '王五',
      productName: '办公椅',
      productSpec: '人体工学 黑色',
      quantity: 1,
      amount: 899.00,
      status: 'shipped',
      statusText: '已发货',
      createTime: '2024-01-14 16:45:00',
      updateTime: '2024-01-15 08:30:00'
    },
    {
      orderNo: 'ORD202401004',
      customerName: '赵六',
      productName: '蓝牙耳机',
      productSpec: '降噪版 白色',
      quantity: 1,
      amount: 299.00,
      status: 'delivered',
      statusText: '已送达',
      createTime: '2024-01-14 14:20:00',
      updateTime: '2024-01-15 16:00:00'
    },
    {
      orderNo: 'ORD202401005',
      customerName: '钱七',
      productName: '咖啡机',
      productSpec: '全自动 不锈钢',
      quantity: 1,
      amount: 1299.00,
      status: 'cancelled',
      statusText: '已取消',
      createTime: '2024-01-13 11:30:00',
      updateTime: '2024-01-14 09:15:00'
    }
  ];

  /**
   * 根据状态获取标签颜色
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'processing';
      case 'shipped':
        return 'purple';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  }

  /**
   * 根据状态获取订单数量
   */
  getOrderCountByStatus(status: string): number {
    return this.orderData.filter(order => order.status === status).length;
  }
}
