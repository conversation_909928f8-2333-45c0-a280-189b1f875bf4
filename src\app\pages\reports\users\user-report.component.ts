import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

/**
 * 用户报表组件
 * 
 * 显示用户相关的统计数据和报表信息
 * 包含用户概览、用户活跃度、用户详细信息等功能
 */
@Component({
  selector: 'app-user-report',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzStatisticModule,
    NzGridModule,
    NzTagModule,
    NzAvatarModule
  ],
  template: `
    <div class="user-report-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="user" nzTheme="outline"></nz-icon>
          用户报表
        </h1>
        <p class="page-description">查看和分析用户数据，了解用户行为和活跃度</p>
      </div>

      <!-- 用户统计概览 -->
      <nz-row [nzGutter]="[16, 16]" class="statistics-row">
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="总用户数"
              nzValue="12,345"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#1890ff' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="活跃用户"
              nzValue="8,967"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#52c41a' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="新增用户"
              nzValue="234"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#722ed1' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="在线用户"
              nzValue="1,567"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#eb2f96' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
      </nz-row>

      <!-- 用户数据表格 -->
      <nz-card nzTitle="用户详情" class="data-table-card">
        <div class="table-actions">
          <button nz-button nzType="primary">
            <nz-icon nzType="download"></nz-icon>
            导出用户数据
          </button>
          <button nz-button>
            <nz-icon nzType="reload"></nz-icon>
            刷新数据
          </button>
        </div>
        
        <nz-table #basicTable [nzData]="userData" [nzPageSize]="10">
          <thead>
            <tr>
              <th>用户头像</th>
              <th>用户名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>注册时间</th>
              <th>最后登录</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of basicTable.data">
              <td>
                <nz-avatar [nzText]="data.username.charAt(0).toUpperCase()"></nz-avatar>
              </td>
              <td>{{ data.username }}</td>
              <td>{{ data.email }}</td>
              <td>
                <nz-tag [nzColor]="getRoleColor(data.role)">{{ data.roleText }}</nz-tag>
              </td>
              <td>{{ data.registerDate }}</td>
              <td>{{ data.lastLogin }}</td>
              <td>
                <span [class]="'status-' + data.status">{{ data.statusText }}</span>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>
    </div>
  `,
  styles: [`
    .user-report-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .statistics-row {
      margin-bottom: 24px;
    }

    .data-table-card {
      margin-top: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .status-active {
      color: #52c41a;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-inactive {
      color: #8c8c8c;
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-banned {
      color: #ff4d4f;
      background: #fff2f0;
      border: 1px solid #ffccc7;
      padding: 2px 8px;
      border-radius: 4px;
    }
  `]
})
export class UserReportComponent {
  /**
   * 用户数据模拟数据
   */
  userData = [
    {
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      roleText: '管理员',
      registerDate: '2023-12-01',
      lastLogin: '2024-01-15 10:30:00',
      status: 'active',
      statusText: '活跃'
    },
    {
      username: 'zhangsan',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      registerDate: '2024-01-10',
      lastLogin: '2024-01-15 09:15:00',
      status: 'active',
      statusText: '活跃'
    },
    {
      username: 'lisi',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      registerDate: '2024-01-08',
      lastLogin: '2024-01-14 16:45:00',
      status: 'inactive',
      statusText: '不活跃'
    },
    {
      username: 'wangwu',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      registerDate: '2024-01-05',
      lastLogin: '2024-01-13 14:20:00',
      status: 'active',
      statusText: '活跃'
    },
    {
      username: 'zhaoliu',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      registerDate: '2024-01-03',
      lastLogin: '2024-01-10 11:30:00',
      status: 'banned',
      statusText: '已禁用'
    }
  ];

  /**
   * 根据角色获取标签颜色
   */
  getRoleColor(role: string): string {
    switch (role) {
      case 'admin':
        return 'red';
      case 'user':
        return 'blue';
      default:
        return 'default';
    }
  }
}
