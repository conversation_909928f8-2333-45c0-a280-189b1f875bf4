import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

import { NzResultModule } from 'ng-zorro-antd/result';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'app-forbidden',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    NzResultModule,
    NzButtonModule
  ],
  template: `
    <div class="error-container">
      <nz-result
        nzStatus="403"
        nzTitle="403"
        nzSubTitle="抱歉，您没有权限访问此页面。">
        <div nz-result-extra>
          <button nz-button nzType="primary" routerLink="/">返回首页</button>
        </div>
      </nz-result>
    </div>
  `,
  styles: [`
    .error-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
  `]
})
export class ForbiddenComponent {}
