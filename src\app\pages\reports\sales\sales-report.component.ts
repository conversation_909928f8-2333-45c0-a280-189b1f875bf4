import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';

/**
 * 销售报表组件
 * 
 * 显示销售相关的统计数据和报表信息
 * 包含销售概览、销售趋势图表、详细数据表格等功能
 */
@Component({
  selector: 'app-sales-report',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzStatisticModule,
    NzGridModule
  ],
  template: `
    <div class="sales-report-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="bar-chart" nzTheme="outline"></nz-icon>
          销售报表
        </h1>
        <p class="page-description">查看和分析销售数据，了解业务表现和趋势</p>
      </div>

      <!-- 统计概览卡片 -->
      <nz-row [nzGutter]="[16, 16]" class="statistics-row">
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="今日销售额"
              nzValue="125,670"
              nzPrefix="¥"
              [nzValueStyle]="{ color: '#3f8600' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="本月销售额"
              nzValue="3,456,789"
              nzPrefix="¥"
              [nzValueStyle]="{ color: '#1890ff' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="订单数量"
              nzValue="1,234"
              nzSuffix="单"
              [nzValueStyle]="{ color: '#722ed1' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="客户数量"
              nzValue="567"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#eb2f96' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
      </nz-row>

      <!-- 销售数据表格 -->
      <nz-card nzTitle="销售明细" class="data-table-card">
        <div class="table-actions">
          <button nz-button nzType="primary">
            <nz-icon nzType="download"></nz-icon>
            导出报表
          </button>
          <button nz-button>
            <nz-icon nzType="reload"></nz-icon>
            刷新数据
          </button>
        </div>
        
        <nz-table #basicTable [nzData]="salesData" [nzPageSize]="10">
          <thead>
            <tr>
              <th>订单编号</th>
              <th>客户名称</th>
              <th>产品名称</th>
              <th>销售金额</th>
              <th>销售日期</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of basicTable.data">
              <td>{{ data.orderNo }}</td>
              <td>{{ data.customerName }}</td>
              <td>{{ data.productName }}</td>
              <td>¥{{ data.amount | number:'1.2-2' }}</td>
              <td>{{ data.saleDate }}</td>
              <td>
                <span [class]="'status-' + data.status">{{ data.statusText }}</span>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>
    </div>
  `,
  styles: [`
    .sales-report-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .statistics-row {
      margin-bottom: 24px;
    }

    .data-table-card {
      margin-top: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .status-completed {
      color: #52c41a;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-pending {
      color: #faad14;
      background: #fffbe6;
      border: 1px solid #ffe58f;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-cancelled {
      color: #ff4d4f;
      background: #fff2f0;
      border: 1px solid #ffccc7;
      padding: 2px 8px;
      border-radius: 4px;
    }
  `]
})
export class SalesReportComponent {
  /**
   * 销售数据模拟数据
   */
  salesData = [
    {
      orderNo: 'SO202401001',
      customerName: '张三',
      productName: '笔记本电脑',
      amount: 5999.00,
      saleDate: '2024-01-15',
      status: 'completed',
      statusText: '已完成'
    },
    {
      orderNo: 'SO202401002',
      customerName: '李四',
      productName: '智能手机',
      amount: 3299.00,
      saleDate: '2024-01-15',
      status: 'pending',
      statusText: '处理中'
    },
    {
      orderNo: 'SO202401003',
      customerName: '王五',
      productName: '平板电脑',
      amount: 2199.00,
      saleDate: '2024-01-14',
      status: 'completed',
      statusText: '已完成'
    },
    {
      orderNo: 'SO202401004',
      customerName: '赵六',
      productName: '智能手表',
      amount: 1299.00,
      saleDate: '2024-01-14',
      status: 'cancelled',
      statusText: '已取消'
    },
    {
      orderNo: 'SO202401005',
      customerName: '钱七',
      productName: '蓝牙耳机',
      amount: 399.00,
      saleDate: '2024-01-13',
      status: 'completed',
      statusText: '已完成'
    }
  ];
}
