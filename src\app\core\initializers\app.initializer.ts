import { inject } from '@angular/core';
import { AuthService } from '../services/auth.service';
import { DynamicRouteService } from '../services/dynamic-route.service';

/**
 * 应用初始化器
 *
 * 在应用启动时执行必要的初始化操作，确保应用状态正确。
 * 主要负责恢复用户认证状态和预加载动态路由配置。
 */
export function initializeApp(): () => Promise<void> {
  const authService = inject(AuthService);
  const dynamicRouteService = inject(DynamicRouteService);

  return async (): Promise<void> => {
    try {
      console.log('🚀 开始应用初始化...');

      // 等待一小段时间，确保服务完全初始化
      await new Promise(resolve => setTimeout(resolve, 100));

      // 检查用户是否已认证（从缓存中恢复状态）
      const isAuthenticated = authService.isAuthenticated();

      if (isAuthenticated) {
        console.log('✅ 用户已认证，开始加载动态路由...');

        // 检查是否已有动态路由配置
        const currentRoutes = dynamicRouteService.routeConfigs();

        if (currentRoutes.length === 0) {
          // 如果没有动态路由配置，则加载并等待完成
          console.log('📡 开始从API加载动态路由配置...');

          try {
            await new Promise<void>((resolve, reject) => {
              const subscription = dynamicRouteService.loadRoutesFromAPI().subscribe({
                next: (routes) => {
                  console.log('✅ 动态路由初始化加载成功:', routes.length, '个路由');
                  subscription.unsubscribe();
                  resolve();
                },
                error: (error) => {
                  console.error('❌ 动态路由初始化加载失败:', error);
                  subscription.unsubscribe();
                  reject(error);
                }
              });

              // 设置超时，避免无限等待
              setTimeout(() => {
                subscription.unsubscribe();
                console.warn('⚠️ 动态路由加载超时，继续启动应用');
                resolve();
              }, 10000); // 10秒超时
            });
          } catch (error) {
            console.error('❌ 动态路由加载失败，但应用继续启动:', error);
          }
        } else {
          console.log('✅ 动态路由已存在，跳过加载');
        }
      } else {
        console.log('ℹ️ 用户未认证，跳过动态路由加载');
      }

      console.log('✅ 应用初始化完成');
    } catch (error) {
      console.error('❌ 应用初始化失败:', error);
      // 不阻止应用启动
    }
  };
}
