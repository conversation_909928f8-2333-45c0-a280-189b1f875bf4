# 加载状态管理文档

## 概述

本文档详细介绍了 Angular 项目中的加载状态管理系统，包括 LoadingService、LoadingComponent 的使用方法，以及在不同场景下实现加载状态的最佳实践。

## 目录

- [加载状态系统架构](#加载状态系统架构)
- [LoadingService - 核心服务](#loadingservice---核心服务)
- [LoadingComponent - 全局加载组件](#loadingcomponent---全局加载组件)
- [使用场景](#使用场景)
- [集成示例](#集成示例)
- [最佳实践](#最佳实践)
- [性能优化](#性能优化)

## 加载状态系统架构

### 核心组件

```
加载状态系统
├── LoadingService          # 核心加载状态管理服务
├── LoadingComponent        # 全局加载指示器组件
├── LoadingState 接口       # 加载状态类型定义
└── 集成机制                # 与其他服务的集成
```

### 系统特性

- **多实例管理**: 支持同时管理多个加载状态
- **进度跟踪**: 支持进度百分比显示
- **消息提示**: 支持自定义加载消息
- **Signal 集成**: 使用 Angular Signal 实现响应式状态管理
- **全局/局部**: 支持全局和局部加载指示器

## LoadingService - 核心服务

### 服务概述

`LoadingService` 是加载状态管理的核心，提供了创建、更新、删除加载状态的完整 API。

### 核心实现

```typescript
export interface LoadingState {
  /** 加载标识符 */
  key: string;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载消息 */
  message?: string;
  /** 进度百分比 */
  progress?: number;
}

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  // 加载状态管理
  private readonly loadingStatesSubject = new BehaviorSubject<Map<string, LoadingState>>(new Map());
  private readonly loadingStatesSignal = signal<Map<string, LoadingState>>(new Map());

  // 公开的只读状态
  readonly loadingStates$ = this.loadingStatesSubject.asObservable();
  readonly loadingStates = this.loadingStatesSignal.asReadonly();

  // 计算属性
  readonly isAnyLoading = computed(() => {
    const states = this.loadingStates();
    return Array.from(states.values()).some(state => state.loading);
  });

  readonly globalLoadingMessage = computed(() => {
    const states = this.loadingStates();
    const loadingState = Array.from(states.values()).find(state => state.loading);
    return loadingState?.message || '';
  });
}
```

### 主要方法

#### 1. 开始加载

```typescript
/**
 * 开始加载
 */
startLoading(key: string, message?: string, progress?: number): void {
  const currentStates = new Map(this.loadingStatesSignal());
  currentStates.set(key, {
    key,
    loading: true,
    message,
    progress
  });
  
  this.updateStates(currentStates);
}
```

#### 2. 停止加载

```typescript
/**
 * 停止加载
 */
stopLoading(key: string): void {
  const currentStates = new Map(this.loadingStatesSignal());
  currentStates.delete(key);
  
  this.updateStates(currentStates);
}
```

#### 3. 更新进度

```typescript
/**
 * 更新加载进度
 */
updateProgress(key: string, progress: number, message?: string): void {
  const currentStates = new Map(this.loadingStatesSignal());
  const existingState = currentStates.get(key);
  
  if (existingState) {
    currentStates.set(key, {
      ...existingState,
      progress,
      message: message || existingState.message
    });
    
    this.updateStates(currentStates);
  }
}
```

#### 4. 状态查询

```typescript
/**
 * 检查指定键是否正在加载
 */
isLoading(key: string): boolean {
  const states = this.loadingStatesSignal();
  const state = states.get(key);
  return state?.loading ?? false;
}

/**
 * 获取指定键的加载状态
 */
getLoadingState(key: string): LoadingState | undefined {
  const states = this.loadingStatesSignal();
  return states.get(key);
}

/**
 * 获取所有正在加载的键
 */
getLoadingKeys(): string[] {
  const states = this.loadingStatesSignal();
  return Array.from(states.values())
    .filter(state => state.loading)
    .map(state => state.key);
}
```

## LoadingComponent - 全局加载组件

### 组件概述

`LoadingComponent` 提供了全局的加载指示器，自动响应 LoadingService 的状态变化。

### 核心实现

```typescript
@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [
    CommonModule,
    NzSpinModule,
    NzProgressModule
  ],
  template: `
    @if (isLoading()) {
      <div class="loading-overlay">
        <div class="loading-content">
          <nz-spin nzSize="large" [nzTip]="loadingMessage()">
            @if (hasProgress()) {
              <div class="loading-progress">
                <nz-progress 
                  [nzPercent]="progress()" 
                  nzStatus="active"
                  [nzShowInfo]="true">
                </nz-progress>
              </div>
            }
          </nz-spin>
        </div>
      </div>
    }
  `,
  styles: [`
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-content {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      min-width: 200px;
    }
    
    .loading-progress {
      margin-top: 16px;
      width: 200px;
    }
  `]
})
export class LoadingComponent {
  private readonly loadingService = inject(LoadingService);

  // 计算属性
  readonly isLoading = computed(() => this.loadingService.isAnyLoading());
  readonly loadingMessage = computed(() => this.loadingService.globalLoadingMessage() || '加载中...');
  
  readonly hasProgress = computed(() => {
    const states = this.loadingService.loadingStates();
    return Array.from(states.values()).some(state => 
      state.loading && typeof state.progress === 'number'
    );
  });
  
  readonly progress = computed(() => {
    const states = this.loadingService.loadingStates();
    const stateWithProgress = Array.from(states.values()).find(state => 
      state.loading && typeof state.progress === 'number'
    );
    return stateWithProgress?.progress || 0;
  });
}
```

### 样式定制

```typescript
// 可定制的加载组件
@Component({
  selector: 'app-custom-loading',
  template: `
    @if (isLoading()) {
      <div class="loading-overlay" [class.dark-theme]="darkMode">
        <div class="loading-content">
          <div class="spinner-container">
            <nz-spin [nzSize]="spinSize" [nzTip]="loadingMessage()">
              <ng-content></ng-content>
            </nz-spin>
          </div>
          
          @if (hasProgress()) {
            <div class="progress-container">
              <nz-progress 
                [nzPercent]="progress()" 
                [nzStatus]="progressStatus"
                [nzStrokeColor]="progressColor">
              </nz-progress>
              <div class="progress-text">
                {{ progress() }}% 完成
              </div>
            </div>
          }
        </div>
      </div>
    }
  `
})
export class CustomLoadingComponent {
  @Input() darkMode = false;
  @Input() spinSize: 'small' | 'default' | 'large' = 'large';
  @Input() progressColor = '#1890ff';
  @Input() progressStatus: 'success' | 'exception' | 'active' | 'normal' = 'active';

  private readonly loadingService = inject(LoadingService);

  readonly isLoading = computed(() => this.loadingService.isAnyLoading());
  readonly loadingMessage = computed(() => this.loadingService.globalLoadingMessage() || '加载中...');
  readonly hasProgress = computed(() => {
    const states = this.loadingService.loadingStates();
    return Array.from(states.values()).some(state => 
      state.loading && typeof state.progress === 'number'
    );
  });
  readonly progress = computed(() => {
    const states = this.loadingService.loadingStates();
    const stateWithProgress = Array.from(states.values()).find(state => 
      state.loading && typeof state.progress === 'number'
    );
    return stateWithProgress?.progress || 0;
  });
}
```

## 使用场景

### 1. API 调用加载

```typescript
@Component({
  selector: 'app-user-list',
  template: `
    <div class="user-list">
      <button nz-button nzType="primary" (click)="loadUsers()" [nzLoading]="isLoadingUsers()">
        加载用户
      </button>

      <nz-table [nzData]="users" [nzLoading]="isLoadingUsers()">
        <!-- 表格内容 -->
      </nz-table>
    </div>
  `
})
export class UserListComponent {
  private readonly loadingService = inject(LoadingService);
  private readonly userService = inject(UserService);

  users: User[] = [];

  // 检查特定加载状态
  isLoadingUsers = computed(() => this.loadingService.isLoading('load-users'));

  loadUsers(): void {
    this.loadingService.startLoading('load-users', '正在加载用户列表...');

    this.userService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
        this.loadingService.stopLoading('load-users');
      },
      error: (error) => {
        console.error('加载用户失败:', error);
        this.loadingService.stopLoading('load-users');
      }
    });
  }
}
```

### 2. 文件上传进度

```typescript
@Component({
  selector: 'app-file-upload',
  template: `
    <div class="file-upload">
      <nz-upload
        nzAction="/api/upload"
        [nzCustomRequest]="customUpload"
        [nzShowUploadList]="false">
        <button nz-button>
          <span nz-icon nzType="upload"></span>
          选择文件
        </button>
      </nz-upload>

      @if (isUploading()) {
        <div class="upload-progress">
          <nz-progress
            [nzPercent]="uploadProgress()"
            nzStatus="active">
          </nz-progress>
          <p>{{ uploadMessage() }}</p>
        </div>
      }
    </div>
  `
})
export class FileUploadComponent {
  private readonly loadingService = inject(LoadingService);
  private readonly uploadService = inject(UploadService);

  isUploading = computed(() => this.loadingService.isLoading('file-upload'));
  uploadProgress = computed(() => {
    const state = this.loadingService.getLoadingState('file-upload');
    return state?.progress || 0;
  });
  uploadMessage = computed(() => {
    const state = this.loadingService.getLoadingState('file-upload');
    return state?.message || '上传中...';
  });

  customUpload = (item: NzUploadXHRArgs): Subscription => {
    this.loadingService.startLoading('file-upload', '准备上传...', 0);

    return this.uploadService.uploadFile(item.file as File).subscribe({
      next: (event) => {
        if (event.type === HttpEventType.UploadProgress) {
          const progress = Math.round(100 * event.loaded / (event.total || 1));
          this.loadingService.updateProgress(
            'file-upload',
            progress,
            `上传中... ${progress}%`
          );
        } else if (event.type === HttpEventType.Response) {
          this.loadingService.stopLoading('file-upload');
          console.log('上传成功:', event.body);
        }
      },
      error: (error) => {
        this.loadingService.stopLoading('file-upload');
        console.error('上传失败:', error);
      }
    });
  };
}
```

### 3. 页面初始化加载

```typescript
@Component({
  selector: 'app-dashboard',
  template: `
    <div class="dashboard">
      @if (isInitializing()) {
        <div class="initializing">
          <nz-spin nzSize="large" nzTip="{{ initMessage() }}">
            <div class="init-content">
              <nz-progress [nzPercent]="initProgress()" nzStatus="active"></nz-progress>
            </div>
          </nz-spin>
        </div>
      } @else {
        <!-- 仪表盘内容 -->
        <div class="dashboard-content">
          <!-- 各种图表和数据 -->
        </div>
      }
    </div>
  `
})
export class DashboardComponent implements OnInit {
  private readonly loadingService = inject(LoadingService);
  private readonly dataService = inject(DataService);

  isInitializing = computed(() => this.loadingService.isLoading('dashboard-init'));
  initProgress = computed(() => {
    const state = this.loadingService.getLoadingState('dashboard-init');
    return state?.progress || 0;
  });
  initMessage = computed(() => {
    const state = this.loadingService.getLoadingState('dashboard-init');
    return state?.message || '初始化中...';
  });

  ngOnInit(): void {
    this.initializeDashboard();
  }

  private async initializeDashboard(): Promise<void> {
    this.loadingService.startLoading('dashboard-init', '正在初始化...', 0);

    try {
      // 步骤1: 加载用户配置
      this.loadingService.updateProgress('dashboard-init', 20, '加载用户配置...');
      await this.dataService.loadUserConfig().toPromise();

      // 步骤2: 加载仪表盘数据
      this.loadingService.updateProgress('dashboard-init', 50, '加载仪表盘数据...');
      await this.dataService.loadDashboardData().toPromise();

      // 步骤3: 加载图表数据
      this.loadingService.updateProgress('dashboard-init', 80, '加载图表数据...');
      await this.dataService.loadChartData().toPromise();

      // 完成
      this.loadingService.updateProgress('dashboard-init', 100, '初始化完成');

      // 延迟一下让用户看到100%
      setTimeout(() => {
        this.loadingService.stopLoading('dashboard-init');
      }, 500);

    } catch (error) {
      this.loadingService.stopLoading('dashboard-init');
      console.error('仪表盘初始化失败:', error);
    }
  }
}
```

### 4. 表单提交加载

```typescript
@Component({
  selector: 'app-user-form',
  template: `
    <form nz-form [formGroup]="userForm" (ngSubmit)="onSubmit()">
      <!-- 表单字段 -->
      <nz-form-item>
        <nz-form-control>
          <input nz-input formControlName="username" placeholder="用户名">
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-control>
          <button
            nz-button
            nzType="primary"
            [nzLoading]="isSubmitting()"
            [disabled]="!userForm.valid">
            {{ submitButtonText() }}
          </button>
        </nz-form-control>
      </nz-form-item>
    </form>
  `
})
export class UserFormComponent {
  private readonly fb = inject(FormBuilder);
  private readonly loadingService = inject(LoadingService);
  private readonly userService = inject(UserService);

  userForm = this.fb.group({
    username: ['', Validators.required],
    email: ['', [Validators.required, Validators.email]]
  });

  isSubmitting = computed(() => this.loadingService.isLoading('submit-user'));
  submitButtonText = computed(() =>
    this.isSubmitting() ? '提交中...' : '提交'
  );

  onSubmit(): void {
    if (this.userForm.valid) {
      this.loadingService.startLoading('submit-user', '正在保存用户信息...');

      this.userService.createUser(this.userForm.value).subscribe({
        next: (user) => {
          this.loadingService.stopLoading('submit-user');
          console.log('用户创建成功:', user);
          this.userForm.reset();
        },
        error: (error) => {
          this.loadingService.stopLoading('submit-user');
          console.error('用户创建失败:', error);
        }
      });
    }
  }
}
```

## 集成示例

### 与 HTTP 拦截器集成

```typescript
@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  private readonly loadingService = inject(LoadingService);
  private activeRequests = new Set<string>();

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 为每个请求生成唯一标识
    const requestId = this.generateRequestId(req);

    // 开始加载
    this.startLoading(requestId, req);

    return next.handle(req).pipe(
      finalize(() => {
        // 请求完成时停止加载
        this.stopLoading(requestId);
      })
    );
  }

  private startLoading(requestId: string, req: HttpRequest<any>): void {
    this.activeRequests.add(requestId);

    // 根据请求类型设置不同的加载消息
    let message = '请求处理中...';
    if (req.method === 'POST') {
      message = '正在保存数据...';
    } else if (req.method === 'GET') {
      message = '正在加载数据...';
    } else if (req.method === 'DELETE') {
      message = '正在删除数据...';
    }

    this.loadingService.startLoading(`http-${requestId}`, message);
  }

  private stopLoading(requestId: string): void {
    this.activeRequests.delete(requestId);
    this.loadingService.stopLoading(`http-${requestId}`);
  }

  private generateRequestId(req: HttpRequest<any>): string {
    return `${req.method}-${req.url}-${Date.now()}`;
  }
}
```

### 与路由守卫集成

```typescript
@Injectable({
  providedIn: 'root'
})
export class LoadingAuthGuard implements CanActivate {
  private readonly authService = inject(AuthService);
  private readonly loadingService = inject(LoadingService);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    this.loadingService.startLoading('auth-check', '验证用户权限...');

    return this.authService.checkAuthStatus().pipe(
      map(isAuthenticated => {
        this.loadingService.stopLoading('auth-check');
        return isAuthenticated;
      }),
      catchError(error => {
        this.loadingService.stopLoading('auth-check');
        return of(false);
      })
    );
  }
}
```

### 局部加载组件

```typescript
@Component({
  selector: 'app-local-loading',
  template: `
    <div class="local-loading-container" [class.loading]="isLoading()">
      @if (isLoading()) {
        <div class="loading-overlay">
          <nz-spin [nzSize]="size" [nzTip]="message()">
            @if (showProgress()) {
              <nz-progress [nzPercent]="progress()" nzStatus="active"></nz-progress>
            }
          </nz-spin>
        </div>
      }

      <div class="content" [class.blurred]="isLoading()">
        <ng-content></ng-content>
      </div>
    </div>
  `,
  styles: [`
    .local-loading-container {
      position: relative;
      min-height: 100px;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .content.blurred {
      filter: blur(1px);
      pointer-events: none;
    }
  `]
})
export class LocalLoadingComponent {
  @Input() loadingKey!: string;
  @Input() size: 'small' | 'default' | 'large' = 'default';

  private readonly loadingService = inject(LoadingService);

  isLoading = computed(() => this.loadingService.isLoading(this.loadingKey));
  message = computed(() => {
    const state = this.loadingService.getLoadingState(this.loadingKey);
    return state?.message || '加载中...';
  });
  progress = computed(() => {
    const state = this.loadingService.getLoadingState(this.loadingKey);
    return state?.progress || 0;
  });
  showProgress = computed(() => {
    const state = this.loadingService.getLoadingState(this.loadingKey);
    return typeof state?.progress === 'number';
  });
}

// 使用示例
@Component({
  template: `
    <app-local-loading loadingKey="user-data">
      <div class="user-content">
        <!-- 用户数据内容 -->
      </div>
    </app-local-loading>
  `
})
export class UserDataComponent {
  private readonly loadingService = inject(LoadingService);

  loadUserData(): void {
    this.loadingService.startLoading('user-data', '加载用户数据...');
    // 加载逻辑...
  }
}
```

## 最佳实践

### 1. 加载键命名规范

```typescript
// ✅ 推荐：使用清晰的命名规范
const loadingKeys = {
  // 按功能模块分组
  USER_LIST: 'user-list',
  USER_CREATE: 'user-create',
  USER_UPDATE: 'user-update',

  // 按页面分组
  DASHBOARD_INIT: 'dashboard-init',
  PROFILE_LOAD: 'profile-load',

  // 按操作类型分组
  FILE_UPLOAD: 'file-upload',
  DATA_EXPORT: 'data-export',
  FORM_SUBMIT: 'form-submit'
};

// ❌ 不推荐：模糊的命名
const badKeys = {
  LOADING1: 'loading1',
  DATA: 'data',
  SUBMIT: 'submit'
};
```

### 2. 自动清理机制

```typescript
@Injectable({
  providedIn: 'root'
})
export class LoadingCleanupService {
  private readonly loadingService = inject(LoadingService);
  private cleanupTimer?: any;

  constructor() {
    this.startCleanupTimer();
  }

  private startCleanupTimer(): void {
    // 每30秒清理一次长时间未更新的加载状态
    this.cleanupTimer = setInterval(() => {
      this.cleanupStaleLoadingStates();
    }, 30000);
  }

  private cleanupStaleLoadingStates(): void {
    const states = this.loadingService.loadingStates();
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5分钟

    states.forEach((state, key) => {
      // 假设我们在状态中添加了时间戳
      const stateAge = now - (state as any).timestamp;
      if (stateAge > staleThreshold) {
        console.warn(`清理过期的加载状态: ${key}`);
        this.loadingService.stopLoading(key);
      }
    });
  }

  ngOnDestroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
  }
}
```

### 3. 错误状态处理

```typescript
// 扩展加载状态接口以支持错误状态
interface ExtendedLoadingState extends LoadingState {
  error?: string;
  retryCount?: number;
  maxRetries?: number;
}

@Injectable({
  providedIn: 'root'
})
export class EnhancedLoadingService extends LoadingService {

  startLoadingWithRetry(
    key: string,
    message?: string,
    maxRetries: number = 3
  ): void {
    const state: ExtendedLoadingState = {
      key,
      loading: true,
      message,
      retryCount: 0,
      maxRetries
    };

    this.updateState(key, state);
  }

  handleError(key: string, error: string): void {
    const currentState = this.getLoadingState(key) as ExtendedLoadingState;
    if (currentState) {
      const retryCount = (currentState.retryCount || 0) + 1;
      const maxRetries = currentState.maxRetries || 3;

      if (retryCount < maxRetries) {
        // 可以重试
        this.updateState(key, {
          ...currentState,
          retryCount,
          message: `${error} (重试 ${retryCount}/${maxRetries})`
        });
      } else {
        // 达到最大重试次数，停止加载
        this.updateState(key, {
          ...currentState,
          loading: false,
          error: `${error} (已达到最大重试次数)`
        });
      }
    }
  }
}
```

### 4. 性能优化

```typescript
// 使用防抖避免频繁更新
@Injectable({
  providedIn: 'root'
})
export class OptimizedLoadingService extends LoadingService {
  private updateQueue = new Map<string, LoadingState>();
  private updateTimer?: any;

  updateProgress(key: string, progress: number, message?: string): void {
    // 将更新加入队列
    const existingState = this.getLoadingState(key);
    if (existingState) {
      this.updateQueue.set(key, {
        ...existingState,
        progress,
        message: message || existingState.message
      });
    }

    // 防抖处理，避免频繁更新
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }

    this.updateTimer = setTimeout(() => {
      this.flushUpdates();
    }, 100); // 100ms 防抖
  }

  private flushUpdates(): void {
    const currentStates = new Map(this.loadingStatesSignal());

    this.updateQueue.forEach((state, key) => {
      currentStates.set(key, state);
    });

    this.updateStates(currentStates);
    this.updateQueue.clear();
  }
}
```

### 5. 测试支持

```typescript
// 测试工具类
@Injectable()
export class LoadingTestUtils {
  constructor(private loadingService: LoadingService) {}

  // 等待加载完成
  waitForLoadingComplete(key: string, timeout: number = 5000): Promise<void> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`加载超时: ${key}`));
      }, timeout);

      const subscription = this.loadingService.loadingStates$.subscribe(states => {
        if (!states.get(key)?.loading) {
          clearTimeout(timer);
          subscription.unsubscribe();
          resolve();
        }
      });
    });
  }

  // 模拟加载状态
  simulateLoading(key: string, duration: number = 2000): Promise<void> {
    this.loadingService.startLoading(key, '模拟加载...');

    return new Promise(resolve => {
      setTimeout(() => {
        this.loadingService.stopLoading(key);
        resolve();
      }, duration);
    });
  }
}

// 测试示例
describe('LoadingService', () => {
  let service: LoadingService;
  let testUtils: LoadingTestUtils;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [LoadingService, LoadingTestUtils]
    });
    service = TestBed.inject(LoadingService);
    testUtils = TestBed.inject(LoadingTestUtils);
  });

  it('should handle loading states correctly', async () => {
    service.startLoading('test', '测试加载...');
    expect(service.isLoading('test')).toBe(true);

    testUtils.simulateLoading('test', 1000);
    await testUtils.waitForLoadingComplete('test');

    expect(service.isLoading('test')).toBe(false);
  });
});
```

## 性能优化

### 1. 虚拟化长列表加载

```typescript
@Component({
  selector: 'app-virtual-list',
  template: `
    <cdk-virtual-scroll-viewport itemSize="50" class="viewport">
      <div *cdkVirtualFor="let item of items; trackBy: trackByFn">
        @if (isItemLoading(item.id)) {
          <nz-skeleton [nzActive]="true"></nz-skeleton>
        } @else {
          <div class="item">{{ item.name }}</div>
        }
      </div>
    </cdk-virtual-scroll-viewport>
  `
})
export class VirtualListComponent {
  private readonly loadingService = inject(LoadingService);

  items: any[] = [];

  isItemLoading(itemId: string): boolean {
    return this.loadingService.isLoading(`item-${itemId}`);
  }

  loadItem(itemId: string): void {
    this.loadingService.startLoading(`item-${itemId}`, '加载项目...');
    // 加载逻辑...
  }

  trackByFn(index: number, item: any): any {
    return item.id;
  }
}
```

### 2. 内存管理

```typescript
@Injectable({
  providedIn: 'root'
})
export class MemoryEfficientLoadingService extends LoadingService {
  private readonly maxStates = 100; // 最大状态数量

  protected updateStates(states: Map<string, LoadingState>): void {
    // 如果状态数量超过限制，清理最旧的状态
    if (states.size > this.maxStates) {
      const sortedStates = Array.from(states.entries())
        .sort((a, b) => (a[1] as any).timestamp - (b[1] as any).timestamp);

      // 保留最新的状态
      const newStates = new Map(sortedStates.slice(-this.maxStates));
      super.updateStates(newStates);
    } else {
      super.updateStates(states);
    }
  }
}
```

---

## 总结

加载状态管理系统为 Angular 应用提供了完整的用户体验解决方案：

1. **统一的状态管理**: 集中管理所有加载状态
2. **灵活的使用方式**: 支持全局和局部加载指示器
3. **丰富的功能**: 进度跟踪、消息提示、错误处理
4. **性能优化**: 防抖、虚拟化、内存管理
5. **易于集成**: 与 HTTP 拦截器、路由守卫等无缝集成

通过遵循本文档中的最佳实践，可以构建用户友好、性能优异的加载体验。
