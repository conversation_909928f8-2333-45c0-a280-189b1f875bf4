# 动态路由管理文档

## 概述

本文档详细介绍了 Angular 项目中的动态路由系统，包括如何动态添加、删除、更新路由，以及路由配置的结构和最佳实践。

## 目录

- [动态路由系统架构](#动态路由系统架构)
- [DynamicRouteService - 核心服务](#dynamicrouteservice---核心服务)
- [路由配置结构](#路由配置结构)
- [动态路由操作](#动态路由操作)
- [懒加载实现](#懒加载实现)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 动态路由系统架构

### 核心组件

```
动态路由系统
├── DynamicRouteService     # 核心路由管理服务
├── DynamicRouteConfig      # 路由配置接口
├── MockApiService          # 模拟 API 服务
├── 路由守卫                # AuthGuard, PermissionGuard
└── 缓存机制                # localStorage 缓存
```

### 系统流程

```
应用启动 → 加载缓存路由 → API 获取最新路由 → 更新路由配置 → 缓存新配置
    ↓
运行时操作 → 添加/删除/更新路由 → 重置 Angular 路由 → 更新缓存
    ↓
权限检查 → 根据用户权限过滤路由 → 生成菜单 → 渲染界面
```

## DynamicRouteService - 核心服务

### 服务概述

`DynamicRouteService` 是动态路由系统的核心，负责路由的增删改查、缓存管理和状态同步。

### 核心实现

```typescript
@Injectable({
  providedIn: 'root'
})
export class DynamicRouteService {
  private readonly router = inject(Router);
  private readonly mockApi = inject(MockApiService);

  // 路由配置状态
  private readonly routeConfigsSubject = new BehaviorSubject<DynamicRouteConfig[]>([]);
  private readonly loadingStateSubject = new BehaviorSubject<RouteLoadingState>({
    loading: false
  });

  // 信号状态
  private readonly routeConfigsSignal = signal<DynamicRouteConfig[]>([]);
  private readonly loadingSignal = signal<RouteLoadingState>({ loading: false });

  // 公开的只读状态
  readonly routeConfigs$ = this.routeConfigsSubject.asObservable();
  readonly loadingState$ = this.loadingStateSubject.asObservable();
  
  // 计算属性
  readonly routeConfigs = this.routeConfigsSignal.asReadonly();
  readonly loadingState = this.loadingSignal.asReadonly();
  readonly isLoading = computed(() => this.loadingState().loading);

  constructor() {
    this.loadCachedRoutes();
  }
}
```

### 主要方法

#### 1. 从 API 加载路由

```typescript
/**
 * 从API加载路由配置
 */
loadRoutesFromAPI(): Observable<DynamicRouteConfig[]> {
  this.setLoadingState({ loading: true });

  return this.mockApi.getRoutes().pipe(
    map(response => {
      if (!response.success) {
        throw new Error(response.message || '加载路由配置失败');
      }
      return response.data;
    }),
    tap(routes => {
      this.updateRouteConfigs(routes);
      this.cacheRoutes(routes);
      this.setLoadingState({ loading: false });
    }),
    catchError(error => {
      this.setLoadingState({
        loading: false,
        error: error.message || '加载路由配置失败'
      });
      return throwError(() => error);
    })
  );
}
```

#### 2. 动态添加路由

```typescript
/**
 * 动态添加路由
 */
addRoute(routeConfig: DynamicRouteConfig): Observable<boolean> {
  try {
    const route = this.convertToAngularRoute(routeConfig);
    this.router.config.push(route);
    this.router.resetConfig(this.router.config);

    // 更新本地状态
    const currentRoutes = this.routeConfigsSignal();
    const updatedRoutes = [...currentRoutes, routeConfig];
    this.updateRouteConfigs(updatedRoutes);
    this.cacheRoutes(updatedRoutes);

    return of(true);
  } catch (error) {
    return throwError(() => error);
  }
}
```

#### 3. 动态删除路由

```typescript
/**
 * 动态删除路由
 */
removeRoute(routeId: string): Observable<boolean> {
  try {
    const currentRoutes = this.routeConfigsSignal();
    const routeToRemove = currentRoutes.find(r => r.id === routeId);
    
    if (!routeToRemove) {
      throw new Error(`路由 ${routeId} 不存在`);
    }

    // 从Angular路由配置中移除
    const routerConfig = this.router.config.filter(r => 
      r.path !== routeToRemove.path
    );
    this.router.resetConfig(routerConfig);

    // 更新本地状态
    const updatedRoutes = currentRoutes.filter(r => r.id !== routeId);
    this.updateRouteConfigs(updatedRoutes);
    this.cacheRoutes(updatedRoutes);

    return of(true);
  } catch (error) {
    return throwError(() => error);
  }
}
```

#### 4. 更新路由配置

```typescript
/**
 * 更新路由配置
 */
updateRoute(routeConfig: DynamicRouteConfig): Observable<boolean> {
  try {
    const currentRoutes = this.routeConfigsSignal();
    const routeIndex = currentRoutes.findIndex(r => r.id === routeConfig.id);
    
    if (routeIndex === -1) {
      throw new Error(`路由 ${routeConfig.id} 不存在`);
    }

    // 更新Angular路由配置
    const route = this.convertToAngularRoute(routeConfig);
    const routerConfig = [...this.router.config];
    const angularRouteIndex = routerConfig.findIndex(r => 
      r.path === currentRoutes[routeIndex].path
    );
    
    if (angularRouteIndex !== -1) {
      routerConfig[angularRouteIndex] = route;
      this.router.resetConfig(routerConfig);
    }

    // 更新本地状态
    const updatedRoutes = [...currentRoutes];
    updatedRoutes[routeIndex] = routeConfig;
    this.updateRouteConfigs(updatedRoutes);
    this.cacheRoutes(updatedRoutes);

    return of(true);
  } catch (error) {
    return throwError(() => error);
  }
}
```

## 路由配置结构

### DynamicRouteConfig 接口

```typescript
export interface DynamicRouteConfig {
  /** 路由ID */
  id: string;
  /** 路由路径 */
  path: string;
  /** 组件名称或组件类型 */
  component?: string | Type<any>;
  /** 懒加载模块路径 */
  loadComponent?: () => Promise<Type<any>>;
  /** 路由标题 */
  title?: string;
  /** 布局类型 */
  layout: LayoutType;
  /** 所需权限 */
  permissions?: string[];
  /** 所需角色 */
  roles?: string[];
  /** 是否需要认证 */
  requireAuth?: boolean;
  /** 路由数据 */
  data?: any;
  /** 子路由 */
  children?: DynamicRouteConfig[];
  /** 重定向路径 */
  redirectTo?: string;
  /** 路径匹配策略 */
  pathMatch?: 'full' | 'prefix';
  /** 是否启用 */
  enabled?: boolean;
  /** 菜单配置 */
  menu?: MenuItem;
  /** 面包屑配置 */
  breadcrumb?: BreadcrumbItem[];
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}
```

### 配置示例

```typescript
// 基本路由配置
const basicRoute: DynamicRouteConfig = {
  id: 'user-profile',
  path: 'profile',
  title: '用户资料',
  layout: LayoutType.DEFAULT,
  requireAuth: true,
  loadComponent: () => import('./user-profile.component').then(m => m.UserProfileComponent),
  menu: {
    id: 'profile',
    title: '用户资料',
    icon: 'user',
    path: '/profile'
  }
};

// 带权限的路由配置
const adminRoute: DynamicRouteConfig = {
  id: 'admin-users',
  path: 'admin/users',
  title: '用户管理',
  layout: LayoutType.DEFAULT,
  requireAuth: true,
  roles: ['admin'],
  permissions: ['user:view'],
  loadComponent: () => import('./admin/user-management.component').then(m => m.UserManagementComponent),
  menu: {
    id: 'admin-users',
    title: '用户管理',
    icon: 'team',
    path: '/admin/users'
  }
};

// 嵌套路由配置
const nestedRoute: DynamicRouteConfig = {
  id: 'reports',
  path: 'reports',
  title: '报表管理',
  layout: LayoutType.DEFAULT,
  requireAuth: true,
  permissions: ['report:view'],
  children: [
    {
      id: 'sales-report',
      path: 'sales',
      title: '销售报表',
      layout: LayoutType.DEFAULT,
      loadComponent: () => import('./reports/sales-report.component').then(m => m.SalesReportComponent)
    },
    {
      id: 'user-report',
      path: 'users',
      title: '用户报表',
      layout: LayoutType.DEFAULT,
      loadComponent: () => import('./reports/user-report.component').then(m => m.UserReportComponent)
    }
  ],
  menu: {
    id: 'reports',
    title: '报表管理',
    icon: 'bar-chart',
    children: [
      {
        id: 'sales-report',
        title: '销售报表',
        path: '/reports/sales'
      },
      {
        id: 'user-report',
        title: '用户报表',
        path: '/reports/users'
      }
    ]
  }
};
```

## 动态路由操作

### 路由转换机制

```typescript
/**
 * 将动态路由配置转换为 Angular 路由
 */
private convertToAngularRoute(routeConfig: DynamicRouteConfig): Route {
  const route: Route = {
    path: routeConfig.path,
    data: {
      ...routeConfig.data,
      layout: routeConfig.layout,
      title: routeConfig.title,
      permissions: routeConfig.permissions,
      roles: routeConfig.roles,
      requireAuth: routeConfig.requireAuth
    }
  };

  if (routeConfig.component) {
    if (typeof routeConfig.component === 'string') {
      // 这里需要根据组件名称动态加载组件
      // 实际项目中可能需要一个组件注册表
      route.loadComponent = routeConfig.loadComponent;
    } else {
      route.component = routeConfig.component;
    }
  } else if (routeConfig.loadComponent) {
    route.loadComponent = routeConfig.loadComponent;
  }

  if (routeConfig.redirectTo) {
    route.redirectTo = routeConfig.redirectTo;
    route.pathMatch = routeConfig.pathMatch || 'full';
  }

  if (routeConfig.children && routeConfig.children.length > 0) {
    route.children = routeConfig.children.map(child =>
      this.convertToAngularRoute(child)
    );
  }

  return route;
}
```

### 缓存管理

```typescript
/**
 * 缓存路由配置
 */
private cacheRoutes(routes: DynamicRouteConfig[]): void {
  try {
    const cacheData = {
      routes,
      timestamp: Date.now()
    };
    localStorage.setItem(
      CACHE_CONFIG.ROUTE_CONFIG_KEY,
      JSON.stringify(cacheData)
    );
    this.cacheTimestamp = cacheData.timestamp;
  } catch (error) {
    console.warn('缓存路由配置失败:', error);
  }
}

/**
 * 加载缓存的路由配置
 */
private loadCachedRoutes(): void {
  try {
    const cachedData = localStorage.getItem(CACHE_CONFIG.ROUTE_CONFIG_KEY);
    if (!cachedData) return;

    const { routes, timestamp } = JSON.parse(cachedData);
    const now = Date.now();

    // 检查缓存是否过期
    if (now - timestamp > CACHE_CONFIG.CACHE_EXPIRE_TIME) {
      this.clearCache();
      return;
    }

    this.updateRouteConfigs(routes);
    this.cacheTimestamp = timestamp;
  } catch (error) {
    console.warn('加载缓存路由配置失败:', error);
    this.clearCache();
  }
}
```

### 权限过滤

```typescript
/**
 * 根据权限获取路由
 */
getRoutesByPermissions(permissions: string[]): DynamicRouteConfig[] {
  return this.routeConfigsSignal().filter(route => {
    if (!route.permissions || route.permissions.length === 0) {
      return true;
    }
    return route.permissions.some(permission =>
      permissions.includes(permission)
    );
  });
}

/**
 * 根据布局类型获取路由
 */
getRoutesByLayout(layout: LayoutType): DynamicRouteConfig[] {
  return this.routeConfigsSignal().filter(route => route.layout === layout);
}
```

## 懒加载实现

### 组件懒加载

```typescript
// 1. 基本懒加载
const lazyRoute: DynamicRouteConfig = {
  id: 'lazy-component',
  path: 'lazy',
  title: '懒加载组件',
  layout: LayoutType.DEFAULT,
  loadComponent: () => import('./lazy/lazy.component').then(m => m.LazyComponent)
};

// 2. 带错误处理的懒加载
const safeRoute: DynamicRouteConfig = {
  id: 'safe-lazy',
  path: 'safe-lazy',
  title: '安全懒加载',
  layout: LayoutType.DEFAULT,
  loadComponent: () => import('./safe-lazy/safe-lazy.component')
    .then(m => m.SafeLazyComponent)
    .catch(error => {
      console.error('组件加载失败:', error);
      // 返回错误组件
      return import('./error/component-load-error.component')
        .then(m => m.ComponentLoadErrorComponent);
    })
};

// 3. 条件懒加载
const conditionalRoute: DynamicRouteConfig = {
  id: 'conditional-lazy',
  path: 'conditional',
  title: '条件懒加载',
  layout: LayoutType.DEFAULT,
  loadComponent: () => {
    // 根据条件加载不同的组件
    const userRole = getCurrentUserRole();
    if (userRole === 'admin') {
      return import('./admin/admin-component').then(m => m.AdminComponent);
    } else {
      return import('./user/user-component').then(m => m.UserComponent);
    }
  }
};
```

### 模块懒加载

```typescript
// 懒加载整个功能模块
const moduleRoute: DynamicRouteConfig = {
  id: 'feature-module',
  path: 'feature',
  title: '功能模块',
  layout: LayoutType.DEFAULT,
  loadComponent: () => import('./feature/feature.routes').then(routes => {
    // 动态注册子路由
    return routes.FeatureRoutingComponent;
  })
};
```

## 使用示例

### 基本使用

```typescript
@Component({
  selector: 'app-route-manager',
  template: `
    <div class="route-manager">
      <h2>动态路由管理</h2>

      <div class="actions">
        <button nz-button nzType="primary" (click)="loadRoutes()">
          加载路由
        </button>
        <button nz-button (click)="addNewRoute()">
          添加路由
        </button>
      </div>

      <nz-table [nzData]="routes()" [nzLoading]="isLoading()">
        <thead>
          <tr>
            <th>ID</th>
            <th>路径</th>
            <th>标题</th>
            <th>布局</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let route of routes()">
            <td>{{ route.id }}</td>
            <td>{{ route.path }}</td>
            <td>{{ route.title }}</td>
            <td>{{ route.layout }}</td>
            <td>
              <button nz-button nzSize="small" (click)="editRoute(route)">
                编辑
              </button>
              <button nz-button nzSize="small" nzDanger (click)="deleteRoute(route.id)">
                删除
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  `
})
export class RouteManagerComponent {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  // 使用 Signal 获取状态
  readonly routes = this.dynamicRouteService.routeConfigs;
  readonly isLoading = this.dynamicRouteService.isLoading;

  loadRoutes(): void {
    this.dynamicRouteService.loadRoutesFromAPI().subscribe({
      next: (routes) => {
        console.log('路由加载成功:', routes);
      },
      error: (error) => {
        console.error('路由加载失败:', error);
      }
    });
  }

  addNewRoute(): void {
    const newRoute: DynamicRouteConfig = {
      id: 'new-route-' + Date.now(),
      path: 'new-page',
      title: '新页面',
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      loadComponent: () => import('./new-page/new-page.component').then(m => m.NewPageComponent)
    };

    this.dynamicRouteService.addRoute(newRoute).subscribe({
      next: (success) => {
        if (success) {
          console.log('路由添加成功');
        }
      },
      error: (error) => {
        console.error('路由添加失败:', error);
      }
    });
  }

  editRoute(route: DynamicRouteConfig): void {
    const updatedRoute: DynamicRouteConfig = {
      ...route,
      title: route.title + ' (已更新)',
      updatedAt: new Date()
    };

    this.dynamicRouteService.updateRoute(updatedRoute).subscribe({
      next: (success) => {
        if (success) {
          console.log('路由更新成功');
        }
      },
      error: (error) => {
        console.error('路由更新失败:', error);
      }
    });
  }

  deleteRoute(routeId: string): void {
    this.dynamicRouteService.removeRoute(routeId).subscribe({
      next: (success) => {
        if (success) {
          console.log('路由删除成功');
        }
      },
      error: (error) => {
        console.error('路由删除失败:', error);
      }
    });
  }
}
```

### 逐步添加新页面指南

#### 步骤 1: 创建组件

```typescript
// 1. 创建新的组件文件 new-feature.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';

@Component({
  selector: 'app-new-feature',
  standalone: true,
  imports: [CommonModule, NzCardModule],
  template: `
    <nz-card nzTitle="新功能页面">
      <p>这是一个动态添加的新功能页面</p>
    </nz-card>
  `
})
export class NewFeatureComponent {}
```

#### 步骤 2: 定义路由配置

```typescript
// 2. 定义路由配置
const newFeatureRoute: DynamicRouteConfig = {
  id: 'new-feature',
  path: 'new-feature',
  title: '新功能',
  layout: LayoutType.DEFAULT,
  requireAuth: true,
  permissions: ['feature:view'],
  loadComponent: () => import('./new-feature/new-feature.component')
    .then(m => m.NewFeatureComponent),
  menu: {
    id: 'new-feature',
    title: '新功能',
    icon: 'star',
    path: '/new-feature',
    order: 100
  },
  breadcrumb: [
    { title: '首页', path: '/', clickable: true },
    { title: '新功能', clickable: false }
  ]
};
```

#### 步骤 3: 添加到路由系统

```typescript
// 3. 在服务中添加路由
@Injectable({
  providedIn: 'root'
})
export class FeatureService {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  addNewFeature(): Observable<boolean> {
    return this.dynamicRouteService.addRoute(newFeatureRoute);
  }
}
```

#### 步骤 4: 在组件中使用

```typescript
// 4. 在管理组件中调用
@Component({
  selector: 'app-admin-panel',
  template: `
    <button nz-button nzType="primary" (click)="addNewFeature()">
      添加新功能
    </button>
  `
})
export class AdminPanelComponent {
  private readonly featureService = inject(FeatureService);

  addNewFeature(): void {
    this.featureService.addNewFeature().subscribe({
      next: (success) => {
        if (success) {
          console.log('新功能添加成功');
          // 可以导航到新页面
          this.router.navigate(['/new-feature']);
        }
      },
      error: (error) => {
        console.error('添加失败:', error);
      }
    });
  }
}
```

### 批量路由操作

```typescript
@Injectable({
  providedIn: 'root'
})
export class BatchRouteService {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  /**
   * 批量添加路由
   */
  addMultipleRoutes(routes: DynamicRouteConfig[]): Observable<boolean[]> {
    const addOperations = routes.map(route =>
      this.dynamicRouteService.addRoute(route)
    );

    return forkJoin(addOperations);
  }

  /**
   * 根据权限批量启用/禁用路由
   */
  toggleRoutesByPermission(permission: string, enabled: boolean): Observable<boolean> {
    const currentRoutes = this.dynamicRouteService.routeConfigs();
    const targetRoutes = currentRoutes.filter(route =>
      route.permissions?.includes(permission)
    );

    const updateOperations = targetRoutes.map(route => {
      const updatedRoute = { ...route, enabled };
      return this.dynamicRouteService.updateRoute(updatedRoute);
    });

    return forkJoin(updateOperations).pipe(
      map(results => results.every(result => result))
    );
  }
}
```

## 最佳实践

### 1. 路由命名规范

```typescript
// ✅ 推荐：使用清晰的命名规范
const routeNamingExamples = {
  // 功能模块路由
  userManagement: 'user-management',
  orderProcessing: 'order-processing',

  // 嵌套路由
  userProfile: 'user/profile',
  userSettings: 'user/settings',

  // 动态参数路由
  userDetail: 'user/:id',
  orderDetail: 'order/:orderId'
};

// ❌ 不推荐：模糊的命名
const badNaming = {
  page1: 'p1',
  userStuff: 'us',
  data: 'data'
};
```

### 2. 权限设计

```typescript
// ✅ 推荐：细粒度权限设计
const permissionExamples = {
  // 按功能模块划分
  userView: 'user:view',
  userCreate: 'user:create',
  userUpdate: 'user:update',
  userDelete: 'user:delete',

  // 按数据范围划分
  userViewOwn: 'user:view:own',
  userViewAll: 'user:view:all',

  // 按操作类型划分
  reportGenerate: 'report:generate',
  reportExport: 'report:export'
};
```

### 3. 错误处理

```typescript
// ✅ 推荐：完善的错误处理
@Injectable({
  providedIn: 'root'
})
export class RouteErrorHandler {
  handleRouteError(error: any, operation: string): Observable<never> {
    let errorMessage = `${operation}失败`;

    if (error.message?.includes('not found')) {
      errorMessage = '路由不存在';
    } else if (error.message?.includes('permission')) {
      errorMessage = '权限不足';
    } else if (error.message?.includes('network')) {
      errorMessage = '网络连接失败';
    }

    // 记录错误日志
    console.error(`路由操作错误 [${operation}]:`, error);

    // 显示用户友好的错误信息
    this.showErrorMessage(errorMessage);

    return throwError(() => new Error(errorMessage));
  }

  private showErrorMessage(message: string): void {
    // 使用消息服务显示错误
  }
}
```

### 4. 性能优化

```typescript
// ✅ 推荐：路由预加载策略
@Injectable({
  providedIn: 'root'
})
export class CustomPreloadingStrategy implements PreloadingStrategy {
  preload(route: Route, load: () => Observable<any>): Observable<any> {
    // 只预加载标记为高优先级的路由
    if (route.data?.['preload'] === true) {
      return load();
    }
    return of(null);
  }
}

// 在路由配置中使用
const highPriorityRoute: DynamicRouteConfig = {
  id: 'dashboard',
  path: 'dashboard',
  title: '仪表盘',
  layout: LayoutType.DEFAULT,
  data: {
    preload: true  // 标记为预加载
  },
  loadComponent: () => import('./dashboard/dashboard.component')
    .then(m => m.DashboardComponent)
};
```

### 5. 测试策略

```typescript
// ✅ 推荐：可测试的路由服务
describe('DynamicRouteService', () => {
  let service: DynamicRouteService;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['resetConfig']);

    TestBed.configureTestingModule({
      providers: [
        DynamicRouteService,
        { provide: Router, useValue: routerSpy }
      ]
    });

    service = TestBed.inject(DynamicRouteService);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should add route successfully', () => {
    const testRoute: DynamicRouteConfig = {
      id: 'test',
      path: 'test',
      title: 'Test',
      layout: LayoutType.DEFAULT
    };

    service.addRoute(testRoute).subscribe(result => {
      expect(result).toBe(true);
      expect(mockRouter.resetConfig).toHaveBeenCalled();
    });
  });
});
```

## 常见问题

### Q1: 如何处理路由冲突？

**A**: 实现路由冲突检测机制：

```typescript
private checkRouteConflict(newRoute: DynamicRouteConfig): boolean {
  const existingRoutes = this.routeConfigsSignal();
  return existingRoutes.some(route =>
    route.path === newRoute.path && route.id !== newRoute.id
  );
}

addRoute(routeConfig: DynamicRouteConfig): Observable<boolean> {
  if (this.checkRouteConflict(routeConfig)) {
    return throwError(() => new Error(`路由路径 ${routeConfig.path} 已存在`));
  }
  // 继续添加路由...
}
```

### Q2: 如何实现路由的版本控制？

**A**: 在路由配置中添加版本信息：

```typescript
interface VersionedRouteConfig extends DynamicRouteConfig {
  version: string;
  deprecated?: boolean;
  migrationPath?: string;
}

const versionedRoute: VersionedRouteConfig = {
  id: 'user-management',
  path: 'users',
  title: '用户管理',
  layout: LayoutType.DEFAULT,
  version: '2.0.0',
  loadComponent: () => import('./users-v2/users.component').then(m => m.UsersComponent)
};
```

### Q3: 如何处理大量路由的性能问题？

**A**: 实现路由分页和虚拟化：

```typescript
@Injectable({
  providedIn: 'root'
})
export class RouteVirtualizationService {
  private readonly pageSize = 50;

  getRoutesByPage(page: number): DynamicRouteConfig[] {
    const allRoutes = this.dynamicRouteService.routeConfigs();
    const startIndex = page * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return allRoutes.slice(startIndex, endIndex);
  }

  loadRoutesOnDemand(routeIds: string[]): Observable<DynamicRouteConfig[]> {
    // 只加载需要的路由
    return this.apiService.getRoutesByIds(routeIds);
  }
}
```

---

## 总结

动态路由系统为 Angular 应用提供了强大的灵活性：

1. **运行时路由管理**: 支持动态添加、删除、更新路由
2. **权限集成**: 与认证系统无缝集成
3. **懒加载支持**: 优化应用性能
4. **缓存机制**: 提升用户体验
5. **类型安全**: 完整的 TypeScript 支持

通过遵循本文档中的最佳实践，可以构建灵活、高效、易维护的动态路由系统。
