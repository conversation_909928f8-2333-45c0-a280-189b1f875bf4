# 认证/登录系统文档

## 概述

本文档详细介绍了 Angular 项目中的认证系统，包括登录流程、令牌管理、用户会话处理和 API 集成。

## 目录

- [认证系统架构](#认证系统架构)
- [AuthService - 认证服务](#authservice---认证服务)
- [登录组件](#登录组件)
- [令牌管理](#令牌管理)
- [用户会话处理](#用户会话处理)
- [API 集成](#api-集成)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)

## 认证系统架构

### 核心组件

```
认证系统
├── AuthService          # 核心认证服务
├── LoginComponent       # 登录界面组件
├── MockApiService       # 模拟 API 服务
├── AuthGuard           # 认证守卫
└── 类型定义             # UserInfo, Role, Permission
```

### 认证流程

```
用户登录 → 验证凭据 → 获取用户信息 → 缓存状态 → 重定向到目标页面
    ↓
持续会话 → 定期验证 → 令牌刷新 → 状态同步
    ↓
用户登出 → 清除状态 → 重定向到登录页
```

## AuthService - 认证服务

### 服务概述

`AuthService` 是认证系统的核心，负责用户登录、登出、状态管理和权限检查。

### 核心实现

```typescript
@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly mockApi = inject(MockApiService);
  private readonly router = inject(Router);

  // 用户状态管理
  private readonly userInfoSubject = new BehaviorSubject<UserInfo | null>(null);
  private readonly userInfoSignal = signal<UserInfo | null>(null);

  // 公开的只读状态
  readonly userInfo$ = this.userInfoSubject.asObservable();
  readonly userInfo = this.userInfoSignal.asReadonly();
  
  // 计算属性
  readonly isAuthenticated = computed(() => {
    const user = this.userInfo();
    return user?.authenticated ?? false;
  });
  
  readonly userPermissions = computed(() => {
    const user = this.userInfo();
    return user?.permissions ?? [];
  });
  
  readonly userRoles = computed(() => {
    const user = this.userInfo();
    return user?.roles ?? [];
  });

  constructor() {
    this.loadCachedUserInfo();
  }
}
```

### 主要方法

#### 1. 用户登录

```typescript
/**
 * 用户登录
 */
login(credentials: { username: string; password: string }): Observable<UserInfo> {
  return this.mockApi.login(credentials).pipe(
    tap(userInfo => {
      this.setUserInfo(userInfo);
      this.cacheUserInfo(userInfo);
    }),
    catchError(error => {
      console.error('登录失败:', error);
      return throwError(() => error);
    })
  );
}
```

#### 2. 用户登出

```typescript
/**
 * 用户登出
 */
logout(): Observable<boolean> {
  // 清除本地状态并重定向
  this.clearUserInfo();
  this.router.navigate([DEFAULT_ROUTES.LOGIN]);
  return of(true);
}
```

#### 3. 权限检查

```typescript
/**
 * 检查用户是否有指定权限
 */
hasPermission(permission: string): boolean {
  const permissions = this.userPermissions();
  return permissions.includes(permission);
}

/**
 * 检查用户是否有任一指定权限
 */
hasAnyPermission(permissions: string[]): boolean {
  const userPermissions = this.userPermissions();
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * 检查用户是否有指定角色
 */
hasRole(roleName: string): boolean {
  const roles = this.userRoles();
  return roles.some(role => role.name === roleName);
}
```

#### 4. 认证状态检查

```typescript
/**
 * 检查认证状态
 */
checkAuthStatus(): Observable<boolean> {
  const currentUser = this.userInfo();
  if (!currentUser?.authenticated) {
    return of(false);
  }

  // 验证令牌是否仍然有效
  return this.getUserInfo().pipe(
    map(() => true),
    catchError(() => {
      this.clearUserInfo();
      return of(false);
    })
  );
}
```

### 状态管理

#### Signal 和 Observable 双重状态

```typescript
// Signal 状态 - 用于组件中的响应式更新
readonly userInfo = this.userInfoSignal.asReadonly();
readonly isAuthenticated = computed(() => {
  const user = this.userInfo();
  return user?.authenticated ?? false;
});

// Observable 状态 - 用于服务间通信
readonly userInfo$ = this.userInfoSubject.asObservable();

// 状态更新方法
private setUserInfo(userInfo: UserInfo): void {
  this.userInfoSignal.set(userInfo);
  this.userInfoSubject.next(userInfo);
}
```

## 登录组件

### 组件概述

`LoginComponent` 提供用户友好的登录界面，集成了表单验证、加载状态和错误处理。

### 核心实现

```typescript
@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCheckboxModule,
    NzIconModule
  ],
  template: `
    <div class="login-container">
      <div class="login-header">
        <h2>用户登录</h2>
        <p>欢迎回来，请输入您的账户信息</p>
      </div>
      
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <nz-form-item>
          <nz-form-control nzErrorTip="请输入用户名">
            <nz-input-group nzPrefixIcon="user">
              <input 
                type="text" 
                nz-input 
                formControlName="username" 
                placeholder="用户名"
                autocomplete="username">
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control nzErrorTip="请输入密码">
            <nz-input-group nzPrefixIcon="lock">
              <input 
                type="password" 
                nz-input 
                formControlName="password" 
                placeholder="密码"
                autocomplete="current-password">
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control>
            <div class="login-options">
              <label nz-checkbox formControlName="remember">记住我</label>
              <a class="forgot-password">忘记密码？</a>
            </div>
          </nz-form-control>
        </nz-form-item>
        
        <nz-form-item>
          <nz-form-control>
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              nzBlock
              [nzLoading]="loading"
              [disabled]="!loginForm.valid">
              登录
            </button>
          </nz-form-control>
        </nz-form-item>
      </form>
      
      <!-- 演示账户 -->
      <div class="demo-accounts">
        <h4>演示账户</h4>
        <div class="demo-account-list">
          <div class="demo-account" (click)="loginAsDemo('admin')">
            <strong>管理员账户</strong>
            <span>admin / admin123</span>
          </div>
          <div class="demo-account" (click)="loginAsDemo('user')">
            <strong>普通用户</strong>
            <span>user / user123</span>
          </div>
        </div>
      </div>
    </div>
  `
})
export class LoginComponent {
  private readonly fb = inject(FormBuilder);
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly message = inject(NzMessageService);

  loading = false;

  loginForm: FormGroup = this.fb.group({
    username: ['', [Validators.required]],
    password: ['', [Validators.required]],
    remember: [false]
  });

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      const { username, password } = this.loginForm.value;

      this.authService.login({ username, password }).subscribe({
        next: (userInfo) => {
          this.loading = false;
          this.message.success('登录成功');

          // 根据用户角色重定向
          if (userInfo.roles.some(role => role.name === 'admin')) {
            this.router.navigate(['/admin/dashboard']);
          } else {
            this.router.navigate(['/user/dashboard']);
          }
        },
        error: (error) => {
          this.loading = false;
          this.message.error(error.message || '登录失败');
        }
      });
    }
  }

  loginAsDemo(type: 'admin' | 'user'): void {
    const credentials = {
      admin: { username: 'admin', password: 'admin123' },
      user: { username: 'user', password: 'user123' }
    };

    this.loginForm.patchValue(credentials[type]);
    this.onSubmit();
  }
}
```

### 关键特性

1. **响应式表单**: 使用 Angular Reactive Forms 进行表单管理
2. **表单验证**: 内置验证规则和错误提示
3. **加载状态**: 登录过程中显示加载指示器
4. **演示账户**: 提供快速登录的演示账户
5. **角色重定向**: 根据用户角色自动重定向到相应页面

## 令牌管理

### 缓存机制

```typescript
/**
 * 缓存用户信息
 */
private cacheUserInfo(userInfo: UserInfo): void {
  try {
    const cacheData = {
      userInfo,
      timestamp: Date.now()
    };
    localStorage.setItem(
      CACHE_CONFIG.USER_INFO_KEY, 
      JSON.stringify(cacheData)
    );
  } catch (error) {
    console.warn('缓存用户信息失败:', error);
  }
}

/**
 * 加载缓存的用户信息
 */
private loadCachedUserInfo(): void {
  try {
    const cachedData = localStorage.getItem(CACHE_CONFIG.USER_INFO_KEY);
    if (!cachedData) return;

    const { userInfo, timestamp } = JSON.parse(cachedData);
    const now = Date.now();
    
    // 检查缓存是否过期
    if (now - timestamp > CACHE_CONFIG.CACHE_EXPIRE_TIME) {
      this.clearCachedUserInfo();
      return;
    }

    this.setUserInfo(userInfo);
  } catch (error) {
    console.warn('加载缓存用户信息失败:', error);
    this.clearCachedUserInfo();
  }
}
```

### 令牌刷新

```typescript
/**
 * 刷新令牌
 */
refreshToken(): Observable<boolean> {
  // 在实际项目中，这里会调用刷新令牌的 API
  return this.mockApi.refreshToken().pipe(
    tap(response => {
      if (response.success) {
        // 更新用户信息中的令牌
        const currentUser = this.userInfo();
        if (currentUser) {
          const updatedUser = {
            ...currentUser,
            token: response.token,
            refreshToken: response.refreshToken
          };
          this.setUserInfo(updatedUser);
          this.cacheUserInfo(updatedUser);
        }
      }
    }),
    map(response => response.success),
    catchError(error => {
      console.error('令牌刷新失败:', error);
      this.logout();
      return of(false);
    })
  );
}
```

### 自动令牌刷新

```typescript
// 在应用启动时设置自动刷新
@Injectable({
  providedIn: 'root'
})
export class TokenRefreshService {
  private readonly authService = inject(AuthService);
  private refreshTimer?: any;

  startAutoRefresh(): void {
    // 每25分钟刷新一次令牌（令牌有效期30分钟）
    this.refreshTimer = setInterval(() => {
      if (this.authService.isAuthenticated()) {
        this.authService.refreshToken().subscribe();
      }
    }, 25 * 60 * 1000);
  }

  stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }
}
```

## 用户会话处理

### 会话状态监控

```typescript
@Injectable({
  providedIn: 'root'
})
export class SessionService {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  // 监控用户活动
  private lastActivity = Date.now();
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30分钟

  constructor() {
    this.setupActivityMonitoring();
    this.setupSessionCheck();
  }

  private setupActivityMonitoring(): void {
    // 监听用户活动事件
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    events.forEach(event => {
      document.addEventListener(event, () => {
        this.lastActivity = Date.now();
      }, true);
    });
  }

  private setupSessionCheck(): void {
    // 每分钟检查一次会话状态
    setInterval(() => {
      if (this.authService.isAuthenticated()) {
        const now = Date.now();
        const timeSinceLastActivity = now - this.lastActivity;

        if (timeSinceLastActivity > this.SESSION_TIMEOUT) {
          this.handleSessionTimeout();
        }
      }
    }, 60 * 1000);
  }

  private handleSessionTimeout(): void {
    // 会话超时处理
    this.authService.logout().subscribe(() => {
      // 显示超时提示
      console.log('会话已超时，请重新登录');
    });
  }
}
```

### 多标签页同步

```typescript
@Injectable({
  providedIn: 'root'
})
export class CrossTabSyncService {
  private readonly authService = inject(AuthService);

  constructor() {
    this.setupStorageListener();
  }

  private setupStorageListener(): void {
    // 监听 localStorage 变化，实现多标签页状态同步
    window.addEventListener('storage', (event) => {
      if (event.key === CACHE_CONFIG.USER_INFO_KEY) {
        if (event.newValue === null) {
          // 用户在其他标签页登出
          this.authService.clearUserInfo();
        } else {
          // 用户在其他标签页登录或更新信息
          try {
            const { userInfo } = JSON.parse(event.newValue);
            this.authService.setUserInfo(userInfo);
          } catch (error) {
            console.warn('同步用户信息失败:', error);
          }
        }
      }
    });
  }
}
```

## API 集成

### MockApiService 实现

```typescript
@Injectable({
  providedIn: 'root'
})
export class MockApiService {
  // 模拟用户数据
  private readonly mockUsers = [
    {
      id: '1',
      username: 'admin',
      password: 'admin123',
      displayName: '系统管理员',
      email: '<EMAIL>',
      roles: [
        {
          id: 'admin',
          name: 'admin',
          permissions: [
            { code: 'system:admin', name: '系统管理', description: '系统管理权限' },
            { code: 'user:view', name: '查看用户', description: '查看用户信息' },
            { code: 'user:edit', name: '编辑用户', description: '编辑用户信息' }
          ]
        }
      ]
    },
    {
      id: '2',
      username: 'user',
      password: 'user123',
      displayName: '普通用户',
      email: '<EMAIL>',
      roles: [
        {
          id: 'user',
          name: 'user',
          permissions: [
            { code: 'user:view', name: '查看用户', description: '查看用户信息' }
          ]
        }
      ]
    }
  ];

  /**
   * 模拟登录 API
   */
  login(credentials: { username: string; password: string }): Observable<UserInfo> {
    return of(null).pipe(
      delay(1000), // 模拟网络延迟
      map(() => {
        const user = this.mockUsers.find(u =>
          u.username === credentials.username && u.password === credentials.password
        );

        if (!user) {
          throw new Error('用户名或密码错误');
        }

        // 构建用户信息
        const userInfo: UserInfo = {
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          email: user.email,
          roles: user.roles,
          permissions: user.roles.flatMap(role => role.permissions.map(p => p.code)),
          authenticated: true
        };

        return userInfo;
      })
    );
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): Observable<UserInfo> {
    // 在实际项目中，这里会验证令牌并返回用户信息
    return of(null).pipe(
      delay(500),
      map(() => {
        // 模拟返回当前用户信息
        const currentUser = this.getCurrentUser();
        if (!currentUser) {
          throw new Error('用户未登录');
        }
        return currentUser;
      })
    );
  }

  /**
   * 刷新令牌
   */
  refreshToken(): Observable<{ success: boolean; token?: string; refreshToken?: string }> {
    return of(null).pipe(
      delay(500),
      map(() => ({
        success: true,
        token: 'new-access-token-' + Date.now(),
        refreshToken: 'new-refresh-token-' + Date.now()
      }))
    );
  }

  private getCurrentUser(): UserInfo | null {
    // 从缓存中获取当前用户信息
    try {
      const cached = localStorage.getItem(CACHE_CONFIG.USER_INFO_KEY);
      if (cached) {
        const { userInfo } = JSON.parse(cached);
        return userInfo;
      }
    } catch (error) {
      console.warn('获取缓存用户信息失败:', error);
    }
    return null;
  }
}
```

### HTTP 拦截器（实际项目中使用）

```typescript
@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private readonly authService = inject(AuthService);

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 为请求添加认证头
    const userInfo = this.authService.userInfo();
    if (userInfo?.authenticated && userInfo.token) {
      const authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${userInfo.token}`)
      });
      return next.handle(authReq);
    }

    return next.handle(req);
  }
}

// 在 app.config.ts 中注册拦截器
export const appConfig: ApplicationConfig = {
  providers: [
    // ... 其他配置
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ]
};
```

## 使用示例

### 基本登录流程

```typescript
// 在组件中使用认证服务
@Component({
  selector: 'app-example',
  template: `
    <div *ngIf="!isAuthenticated()">
      <button (click)="login()">登录</button>
    </div>

    <div *ngIf="isAuthenticated()">
      <p>欢迎，{{ userInfo()?.displayName }}</p>
      <button (click)="logout()">登出</button>
    </div>
  `
})
export class ExampleComponent {
  private readonly authService = inject(AuthService);

  // 使用 Signal 获取认证状态
  readonly isAuthenticated = this.authService.isAuthenticated;
  readonly userInfo = this.authService.userInfo;

  login(): void {
    this.authService.login({ username: 'admin', password: 'admin123' }).subscribe({
      next: (userInfo) => {
        console.log('登录成功:', userInfo);
      },
      error: (error) => {
        console.error('登录失败:', error);
      }
    });
  }

  logout(): void {
    this.authService.logout().subscribe();
  }
}
```

### 权限检查示例

```typescript
@Component({
  selector: 'app-admin-panel',
  template: `
    <div class="admin-panel">
      <h2>管理面板</h2>

      <!-- 基于权限显示内容 -->
      <div *ngIf="canViewUsers()">
        <h3>用户管理</h3>
        <button *ngIf="canEditUsers()" (click)="editUser()">编辑用户</button>
      </div>

      <div *ngIf="canManageSystem()">
        <h3>系统设置</h3>
        <button (click)="openSettings()">系统配置</button>
      </div>
    </div>
  `
})
export class AdminPanelComponent {
  private readonly authService = inject(AuthService);

  canViewUsers(): boolean {
    return this.authService.hasPermission('user:view');
  }

  canEditUsers(): boolean {
    return this.authService.hasPermission('user:edit');
  }

  canManageSystem(): boolean {
    return this.authService.hasRole('admin');
  }
}
```

### 自动重定向示例

```typescript
@Component({
  selector: 'app-dashboard',
  template: `<div>仪表盘内容</div>`
})
export class DashboardComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  ngOnInit(): void {
    // 检查认证状态，未认证则重定向到登录页
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: this.router.url }
      });
    }
  }
}
```

## 最佳实践

### 1. 安全存储

```typescript
// ✅ 推荐：使用安全的存储方式
class SecureStorage {
  private readonly key = 'app_user_data';

  setItem(data: any): void {
    try {
      // 在实际项目中，可以考虑加密存储
      const encrypted = this.encrypt(JSON.stringify(data));
      localStorage.setItem(this.key, encrypted);
    } catch (error) {
      console.warn('存储失败:', error);
    }
  }

  getItem(): any {
    try {
      const encrypted = localStorage.getItem(this.key);
      if (encrypted) {
        return JSON.parse(this.decrypt(encrypted));
      }
    } catch (error) {
      console.warn('读取失败:', error);
    }
    return null;
  }

  private encrypt(data: string): string {
    // 实现加密逻辑
    return btoa(data); // 简单的 base64 编码，实际项目中应使用更安全的加密
  }

  private decrypt(data: string): string {
    // 实现解密逻辑
    return atob(data);
  }
}
```

### 2. 错误处理

```typescript
// ✅ 推荐：统一的错误处理
@Injectable({
  providedIn: 'root'
})
export class AuthErrorHandler {
  handleAuthError(error: any): Observable<never> {
    let errorMessage = '认证失败';

    if (error.status === 401) {
      errorMessage = '用户名或密码错误';
    } else if (error.status === 403) {
      errorMessage = '权限不足';
    } else if (error.status === 0) {
      errorMessage = '网络连接失败';
    }

    // 显示错误提示
    this.showErrorMessage(errorMessage);

    return throwError(() => new Error(errorMessage));
  }

  private showErrorMessage(message: string): void {
    // 使用 NZ-ZORRO 的消息服务显示错误
    // this.message.error(message);
  }
}
```

### 3. 性能优化

```typescript
// ✅ 推荐：使用 OnPush 策略优化性能
@Component({
  selector: 'app-user-info',
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="user-info">
      <span>{{ userInfo()?.displayName }}</span>
      <span *ngIf="isAdmin()">管理员</span>
    </div>
  `
})
export class UserInfoComponent {
  private readonly authService = inject(AuthService);

  // 使用 Signal 自动处理变更检测
  readonly userInfo = this.authService.userInfo;
  readonly isAdmin = computed(() =>
    this.authService.hasRole('admin')
  );
}
```

### 4. 测试友好的设计

```typescript
// ✅ 推荐：便于测试的服务设计
@Injectable({
  providedIn: 'root'
})
export class AuthService {
  constructor(
    private apiService: ApiService,
    private storageService: StorageService,
    private router: Router
  ) {}

  // 提供测试用的方法
  setUserInfoForTesting(userInfo: UserInfo): void {
    if (environment.production) {
      throw new Error('此方法仅用于测试环境');
    }
    this.setUserInfo(userInfo);
  }
}

// 测试示例
describe('AuthService', () => {
  let service: AuthService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AuthService,
        { provide: ApiService, useClass: MockApiService },
        { provide: StorageService, useClass: MockStorageService }
      ]
    });
    service = TestBed.inject(AuthService);
  });

  it('should authenticate user', () => {
    const mockUser: UserInfo = {
      id: '1',
      username: 'test',
      authenticated: true,
      // ... 其他属性
    };

    service.setUserInfoForTesting(mockUser);
    expect(service.isAuthenticated()).toBe(true);
  });
});
```

---

## 总结

认证系统是应用安全的基础，本项目实现了：

1. **完整的认证流程**: 登录、登出、状态管理
2. **灵活的权限控制**: 基于角色和权限的访问控制
3. **良好的用户体验**: 自动重定向、状态同步、错误处理
4. **现代化的技术栈**: Signal、Observable、Standalone Components
5. **可扩展的架构**: 易于集成真实的后端 API

通过遵循本文档中的最佳实践，可以构建安全、高效、易维护的认证系统。
