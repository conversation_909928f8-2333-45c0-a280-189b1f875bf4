import { Injectable, Type, signal, computed, inject } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter, map } from 'rxjs/operators';

import { LayoutType, LayoutConfig } from '../types';
import { LAYOUT_ROUTE_MAPPING } from '../constants';

// 导入布局组件
import {
  DefaultLayoutComponent,
  AuthLayoutComponent,
  BlankLayoutComponent
} from '../../layouts';

@Injectable({
  providedIn: 'root'
})
export class LayoutService {
  private readonly router = inject(Router);
  
  // 布局组件映射
  private readonly layoutComponents = new Map<LayoutType, Type<any>>([
    [LayoutType.DEFAULT, DefaultLayoutComponent],
    [LayoutType.AUTH, AuthLayoutComponent],
    [LayoutType.BLANK, BlankLayoutComponent],
    [LayoutType.FULL, BlankLayoutComponent] // 全屏布局使用空白布局
  ]);

  // 当前布局状态
  private readonly currentLayoutSignal = signal<LayoutType>(LayoutType.DEFAULT);
  private readonly layoutConfigSignal = signal<LayoutConfig>({
    type: LayoutType.DEFAULT,
    showHeader: true,
    showSidebar: true,
    showBreadcrumb: true,
    showFooter: true,
    sidebarCollapsible: true,
    sidebarCollapsed: false
  });

  // 公开的只读状态
  readonly currentLayout = this.currentLayoutSignal.asReadonly();
  readonly layoutConfig = this.layoutConfigSignal.asReadonly();
  
  // 计算属性
  readonly currentLayoutComponent = computed(() => {
    const layout = this.currentLayout();
    return this.layoutComponents.get(layout) || BlankLayoutComponent;
  });

  constructor() {
    this.setupLayoutDetection();
  }

  /**
   * 根据路由路径获取布局类型
   */
  getLayoutByPath(path: string): LayoutType {
    // 检查路径是否匹配预定义的布局映射
    for (const [layout, routePrefix] of Object.entries(LAYOUT_ROUTE_MAPPING)) {
      if (path.startsWith(routePrefix)) {
        return layout as LayoutType;
      }
    }

    // 默认返回默认布局
    return LayoutType.DEFAULT;
  }

  /**
   * 根据路由数据获取布局类型
   */
  getLayoutFromRouteData(routeData: any): LayoutType {
    return routeData?.layout || LayoutType.DEFAULT;
  }

  /**
   * 设置当前布局
   */
  setLayout(layout: LayoutType, config?: Partial<LayoutConfig>): void {
    this.currentLayoutSignal.set(layout);
    
    if (config) {
      this.updateLayoutConfig(config);
    } else {
      // 设置默认配置
      this.setDefaultLayoutConfig(layout);
    }
  }

  /**
   * 更新布局配置
   */
  updateLayoutConfig(config: Partial<LayoutConfig>): void {
    const currentConfig = this.layoutConfigSignal();
    this.layoutConfigSignal.set({
      ...currentConfig,
      ...config
    });
  }

  /**
   * 获取布局组件
   */
  getLayoutComponent(layout: LayoutType): Type<any> {
    return this.layoutComponents.get(layout) || BlankLayoutComponent;
  }

  /**
   * 注册自定义布局组件
   */
  registerLayoutComponent(layout: LayoutType, component: Type<any>): void {
    this.layoutComponents.set(layout, component);
  }

  /**
   * 检查布局是否支持侧边栏
   */
  hasSidebar(layout?: LayoutType): boolean {
    const currentLayout = layout || this.currentLayout();
    return [LayoutType.DEFAULT, LayoutType.ADMIN].includes(currentLayout);
  }

  /**
   * 检查布局是否支持头部导航
   */
  hasHeader(layout?: LayoutType): boolean {
    const currentLayout = layout || this.currentLayout();
    return [LayoutType.DEFAULT, LayoutType.ADMIN, LayoutType.USER].includes(currentLayout);
  }

  /**
   * 检查布局是否支持面包屑
   */
  hasBreadcrumb(layout?: LayoutType): boolean {
    const currentLayout = layout || this.currentLayout();
    return [LayoutType.DEFAULT, LayoutType.ADMIN, LayoutType.USER].includes(currentLayout);
  }

  /**
   * 检查布局是否支持页脚
   */
  hasFooter(layout?: LayoutType): boolean {
    const currentLayout = layout || this.currentLayout();
    return [LayoutType.DEFAULT, LayoutType.ADMIN, LayoutType.USER].includes(currentLayout);
  }

  // 私有方法

  private setupLayoutDetection(): void {
    // 监听路由变化，自动检测布局
    if (typeof window !== 'undefined' && this.router) {
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd),
        map(() => this.getActiveRoute())
      ).subscribe(route => {
        this.detectLayoutFromRoute(route);
      });
    }
  }

  private getActiveRoute(): ActivatedRoute {
    let route = this.router.routerState.root;
    while (route.firstChild) {
      route = route.firstChild;
    }
    return route;
  }

  private detectLayoutFromRoute(route: ActivatedRoute): void {
    // 首先检查路由数据中的布局配置
    const routeData = route.snapshot.data;
    let layout = this.getLayoutFromRouteData(routeData);
    
    // 如果路由数据中没有布局配置，根据路径推断
    if (layout === LayoutType.DEFAULT) {
      const path = route.snapshot.url.join('/');
      layout = this.getLayoutByPath('/' + path);
    }

    // 设置布局
    this.setLayout(layout);
  }

  private setDefaultLayoutConfig(layout: LayoutType): void {
    const defaultConfigs: Record<LayoutType, LayoutConfig> = {
      [LayoutType.DEFAULT]: {
        type: LayoutType.DEFAULT,
        showHeader: true,
        showSidebar: true,
        showBreadcrumb: true,
        showFooter: true,
        sidebarCollapsible: true,
        sidebarCollapsed: false
      },
      [LayoutType.ADMIN]: {
        type: LayoutType.ADMIN,
        showHeader: true,
        showSidebar: true,
        showBreadcrumb: true,
        showFooter: true,
        sidebarCollapsible: true,
        sidebarCollapsed: false
      },
      [LayoutType.USER]: {
        type: LayoutType.USER,
        showHeader: true,
        showSidebar: true, // 用户也显示侧边栏
        showBreadcrumb: true,
        showFooter: true,
        sidebarCollapsible: true, // 用户侧边栏也可折叠
        sidebarCollapsed: false
      },
      [LayoutType.AUTH]: {
        type: LayoutType.AUTH,
        showHeader: false,
        showSidebar: false,
        showBreadcrumb: false,
        showFooter: false,
        sidebarCollapsible: false,
        sidebarCollapsed: false
      },
      [LayoutType.BLANK]: {
        type: LayoutType.BLANK,
        showHeader: false,
        showSidebar: false,
        showBreadcrumb: false,
        showFooter: false,
        sidebarCollapsible: false,
        sidebarCollapsed: false
      },
      [LayoutType.FULL]: {
        type: LayoutType.FULL,
        showHeader: false,
        showSidebar: false,
        showBreadcrumb: false,
        showFooter: false,
        sidebarCollapsible: false,
        sidebarCollapsed: false
      }
    };

    this.layoutConfigSignal.set(defaultConfigs[layout]);
  }
}
