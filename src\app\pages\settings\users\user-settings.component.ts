import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

/**
 * 用户设置组件
 * 
 * 管理系统用户的增删改查功能
 * 包含用户列表、添加用户、编辑用户、删除用户等功能
 */
@Component({
  selector: 'app-user-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzModalModule,
    NzPopconfirmModule,
    NzTagModule,
    NzAvatarModule
  ],
  template: `
    <div class="user-settings-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="user" nzTheme="outline"></nz-icon>
          用户设置
        </h1>
        <p class="page-description">管理系统用户，包括添加、编辑、删除用户等操作</p>
      </div>

      <!-- 用户管理表格 -->
      <nz-card nzTitle="用户管理" class="user-table-card">
        <div class="table-actions">
          <button nz-button nzType="primary" (click)="showAddUserModal()">
            <nz-icon nzType="plus"></nz-icon>
            添加用户
          </button>
          <button nz-button>
            <nz-icon nzType="reload"></nz-icon>
            刷新
          </button>
        </div>
        
        <nz-table #basicTable [nzData]="userList" [nzPageSize]="10">
          <thead>
            <tr>
              <th>头像</th>
              <th>用户名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of basicTable.data">
              <td>
                <nz-avatar [nzText]="user.username.charAt(0).toUpperCase()"></nz-avatar>
              </td>
              <td>{{ user.username }}</td>
              <td>{{ user.email }}</td>
              <td>
                <nz-tag [nzColor]="getRoleColor(user.role)">{{ user.roleText }}</nz-tag>
              </td>
              <td>
                <span [class]="'status-' + user.status">{{ user.statusText }}</span>
              </td>
              <td>{{ user.createTime }}</td>
              <td>
                <button nz-button nzType="link" nzSize="small" (click)="editUser(user)">
                  <nz-icon nzType="edit"></nz-icon>
                  编辑
                </button>
                <button 
                  nz-button 
                  nzType="link" 
                  nzSize="small" 
                  nzDanger
                  nz-popconfirm
                  nzPopconfirmTitle="确定要删除这个用户吗？"
                  (nzOnConfirm)="deleteUser(user.id)">
                  <nz-icon nzType="delete"></nz-icon>
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 添加/编辑用户模态框 -->
      <nz-modal
        [(nzVisible)]="isModalVisible"
        [nzTitle]="modalTitle"
        (nzOnCancel)="handleCancel()"
        (nzOnOk)="handleOk()"
        [nzOkLoading]="isLoading">
        <ng-container *nzModalContent>
          <form nz-form [formGroup]="userForm" [nzLayout]="'vertical'">
            <nz-form-item>
              <nz-form-label nzRequired>用户名</nz-form-label>
              <nz-form-control nzErrorTip="请输入用户名">
                <input nz-input formControlName="username" placeholder="请输入用户名" />
              </nz-form-control>
            </nz-form-item>
            
            <nz-form-item>
              <nz-form-label nzRequired>邮箱</nz-form-label>
              <nz-form-control nzErrorTip="请输入有效的邮箱地址">
                <input nz-input formControlName="email" placeholder="请输入邮箱" />
              </nz-form-control>
            </nz-form-item>
            
            <nz-form-item>
              <nz-form-label nzRequired>角色</nz-form-label>
              <nz-form-control nzErrorTip="请选择角色">
                <nz-select formControlName="role" nzPlaceHolder="请选择角色">
                  <nz-option nzValue="admin" nzLabel="管理员"></nz-option>
                  <nz-option nzValue="user" nzLabel="普通用户"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            
            <nz-form-item>
              <nz-form-label nzRequired>状态</nz-form-label>
              <nz-form-control nzErrorTip="请选择状态">
                <nz-select formControlName="status" nzPlaceHolder="请选择状态">
                  <nz-option nzValue="active" nzLabel="活跃"></nz-option>
                  <nz-option nzValue="inactive" nzLabel="不活跃"></nz-option>
                  <nz-option nzValue="banned" nzLabel="已禁用"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </form>
        </ng-container>
      </nz-modal>
    </div>
  `,
  styles: [`
    .user-settings-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .user-table-card {
      margin-top: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .status-active {
      color: #52c41a;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-inactive {
      color: #8c8c8c;
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      padding: 2px 8px;
      border-radius: 4px;
    }

    .status-banned {
      color: #ff4d4f;
      background: #fff2f0;
      border: 1px solid #ffccc7;
      padding: 2px 8px;
      border-radius: 4px;
    }
  `]
})
export class UserSettingsComponent {
  isModalVisible = false;
  isLoading = false;
  modalTitle = '添加用户';
  currentUserId: string | null = null;

  userForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.userForm = this.fb.group({
      username: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      role: ['', [Validators.required]],
      status: ['', [Validators.required]]
    });
  }

  /**
   * 用户列表模拟数据
   */
  userList = [
    {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      roleText: '管理员',
      status: 'active',
      statusText: '活跃',
      createTime: '2023-12-01 10:00:00'
    },
    {
      id: '2',
      username: 'zhangsan',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      status: 'active',
      statusText: '活跃',
      createTime: '2024-01-10 14:30:00'
    },
    {
      id: '3',
      username: 'lisi',
      email: '<EMAIL>',
      role: 'user',
      roleText: '普通用户',
      status: 'inactive',
      statusText: '不活跃',
      createTime: '2024-01-08 09:15:00'
    }
  ];

  /**
   * 显示添加用户模态框
   */
  showAddUserModal(): void {
    this.modalTitle = '添加用户';
    this.currentUserId = null;
    this.userForm.reset();
    this.isModalVisible = true;
  }

  /**
   * 编辑用户
   */
  editUser(user: any): void {
    this.modalTitle = '编辑用户';
    this.currentUserId = user.id;
    this.userForm.patchValue({
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status
    });
    this.isModalVisible = true;
  }

  /**
   * 删除用户
   */
  deleteUser(userId: string): void {
    this.userList = this.userList.filter(user => user.id !== userId);
    this.message.success('用户删除成功');
  }

  /**
   * 模态框确认
   */
  handleOk(): void {
    if (this.userForm.valid) {
      this.isLoading = true;
      
      // 模拟API调用
      setTimeout(() => {
        const formValue = this.userForm.value;
        
        if (this.currentUserId) {
          // 编辑用户
          const userIndex = this.userList.findIndex(user => user.id === this.currentUserId);
          if (userIndex > -1) {
            this.userList[userIndex] = {
              ...this.userList[userIndex],
              ...formValue,
              roleText: formValue.role === 'admin' ? '管理员' : '普通用户',
              statusText: this.getStatusText(formValue.status)
            };
          }
          this.message.success('用户更新成功');
        } else {
          // 添加用户
          const newUser = {
            id: Date.now().toString(),
            ...formValue,
            roleText: formValue.role === 'admin' ? '管理员' : '普通用户',
            statusText: this.getStatusText(formValue.status),
            createTime: new Date().toLocaleString()
          };
          this.userList = [...this.userList, newUser];
          this.message.success('用户添加成功');
        }
        
        this.isLoading = false;
        this.isModalVisible = false;
        this.userForm.reset();
      }, 1000);
    } else {
      Object.values(this.userForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  /**
   * 模态框取消
   */
  handleCancel(): void {
    this.isModalVisible = false;
    this.userForm.reset();
  }

  /**
   * 根据角色获取标签颜色
   */
  getRoleColor(role: string): string {
    switch (role) {
      case 'admin':
        return 'red';
      case 'user':
        return 'blue';
      default:
        return 'default';
    }
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    switch (status) {
      case 'active':
        return '活跃';
      case 'inactive':
        return '不活跃';
      case 'banned':
        return '已禁用';
      default:
        return '未知';
    }
  }
}
