# 布局系统文档

## 概述

本文档详细介绍了 Angular 项目中的多布局系统，包括布局架构设计、各布局组件的实现、布局服务的使用方法以及响应式设计的实现。

## 目录

- [布局系统架构](#布局系统架构)
- [LayoutService - 布局管理服务](#layoutservice---布局管理服务)
- [布局组件详解](#布局组件详解)
- [布局配置和切换](#布局配置和切换)
- [响应式设计](#响应式设计)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)

## 布局系统架构

### 设计理念

本项目采用多布局系统设计，根据不同的业务场景和用户角色提供不同的界面布局：

- **统一后台布局** - admin 和 user 角色都使用相同的管理界面布局
- **认证布局** - 专门用于登录、注册等认证相关页面
- **空白布局** - 适用于特殊页面或全屏显示的场景

### 核心组件

```
布局系统
├── LayoutService           # 布局管理服务
├── DefaultLayoutComponent  # 默认后台管理布局
├── AuthLayoutComponent     # 认证页面布局
├── BlankLayoutComponent    # 空白布局
└── 布局类型定义            # LayoutType, LayoutConfig
```

### 布局类型枚举

```typescript
export enum LayoutType {
  DEFAULT = 'default',    // 默认后台管理布局
  AUTH = 'auth',         // 认证页面布局
  BLANK = 'blank',       // 空白布局
  FULL = 'full'          // 全屏布局（使用空白布局实现）
}
```

## LayoutService - 布局管理服务

### 服务概述

`LayoutService` 是布局系统的核心服务，负责布局的选择、配置和状态管理。

### 核心功能

#### 1. 布局组件映射

```typescript
private readonly layoutComponents = new Map<LayoutType, Type<any>>([
  [LayoutType.DEFAULT, DefaultLayoutComponent],
  [LayoutType.AUTH, AuthLayoutComponent],
  [LayoutType.BLANK, BlankLayoutComponent],
  [LayoutType.FULL, BlankLayoutComponent]
]);
```

#### 2. 布局状态管理

```typescript
// 当前布局状态
private readonly currentLayoutSignal = signal<LayoutType>(LayoutType.DEFAULT);
private readonly layoutConfigSignal = signal<LayoutConfig>({
  type: LayoutType.DEFAULT,
  showHeader: true,
  showSidebar: true,
  showBreadcrumb: true,
  showFooter: true,
  sidebarCollapsible: true,
  sidebarCollapsed: false
});

// 公开的只读状态
readonly currentLayout = this.currentLayoutSignal.asReadonly();
readonly layoutConfig = this.layoutConfigSignal.asReadonly();
```

#### 3. 主要方法

**根据路径获取布局类型**
```typescript
getLayoutByPath(path: string): LayoutType {
  for (const [layout, routePrefix] of Object.entries(LAYOUT_ROUTE_MAPPING)) {
    if (path.startsWith(routePrefix)) {
      return layout as LayoutType;
    }
  }
  return LayoutType.DEFAULT;
}
```

**设置当前布局**
```typescript
setLayout(layout: LayoutType, config?: Partial<LayoutConfig>): void {
  this.currentLayoutSignal.set(layout);
  
  if (config) {
    this.updateLayoutConfig(config);
  } else {
    this.setDefaultLayoutConfig(layout);
  }
}
```

**注册自定义布局组件**
```typescript
registerLayoutComponent(layout: LayoutType, component: Type<any>): void {
  this.layoutComponents.set(layout, component);
}
```

## 布局组件详解

### 1. DefaultLayoutComponent - 默认后台管理布局

#### 设计特点
- **统一设计** - admin 和 user 角色使用相同的布局
- **侧边栏导航** - 所有用户都显示侧边栏菜单
- **响应式设计** - 支持移动端自适应
- **面包屑导航** - 自动生成导航路径

#### 核心实现
```typescript
@Component({
  selector: 'app-default-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NzLayoutModule,
    NzMenuModule,
    NzBreadCrumbModule,
    NzIconModule,
    NzAvatarModule,
    NzDropDownModule,
    NzButtonModule
  ],
  template: `
    <nz-layout class="default-layout">
      <!-- 侧边栏 -->
      <nz-sider 
        nzCollapsible 
        [(nzCollapsed)]="isCollapsed"
        [nzBreakpoint]="'lg'"
        class="layout-sider">
        <!-- 侧边栏内容 -->
      </nz-sider>
      
      <!-- 主内容区域 -->
      <nz-layout class="layout-content">
        <!-- 头部 -->
        <nz-header class="layout-header">
          <!-- 头部内容 -->
        </nz-header>
        
        <!-- 面包屑 -->
        <div class="breadcrumb-container">
          <nz-breadcrumb>
            <!-- 面包屑项目 -->
          </nz-breadcrumb>
        </div>
        
        <!-- 内容区域 -->
        <nz-content class="layout-main-content">
          <router-outlet></router-outlet>
        </nz-content>
        
        <!-- 页脚 -->
        <nz-footer class="layout-footer">
          {{ footerText() }}
        </nz-footer>
      </nz-layout>
    </nz-layout>
  `
})
export class DefaultLayoutComponent implements OnInit {
  // 状态管理
  readonly isCollapsed = signal(false);
  readonly dynamicMenuItems = signal<MenuItem[]>([]);
  readonly breadcrumbs = signal<BreadcrumbItem[]>([]);

  // 计算属性
  readonly userInfo = computed(() => this.authService.userInfo());
  readonly showSidebar = computed(() => true); // 所有用户都显示侧边栏
  readonly pageTitle = computed(() => '后台管理系统');
  readonly footerText = computed(() => 'Management System');
}
```

### 2. AuthLayoutComponent - 认证页面布局

#### 设计特点
- **双栏设计** - 左侧品牌展示，右侧表单区域
- **渐变背景** - 现代化的视觉设计
- **响应式适配** - 移动端自动切换为单栏布局
- **品牌展示** - 左侧显示系统品牌信息

#### 核心实现
```typescript
@Component({
  selector: 'app-auth-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    NzLayoutModule,
    NzCardModule
  ],
  template: `
    <nz-layout class="auth-layout">
      <nz-content class="auth-content">
        <div class="auth-container">
          <!-- 左侧背景区域 -->
          <div class="auth-background">
            <div class="background-overlay">
              <div class="brand-info">
                <img src="/assets/logo-white.png" alt="Logo" class="brand-logo">
                <h1 class="brand-title">企业管理系统</h1>
                <p class="brand-description">
                  基于 Angular 和 NG-ZORRO 构建的现代化企业级管理平台
                </p>
              </div>
            </div>
          </div>
          
          <!-- 右侧表单区域 -->
          <div class="auth-form-container">
            <div class="auth-form-wrapper">
              <router-outlet></router-outlet>
            </div>
          </div>
        </div>
      </nz-content>
    </nz-layout>
  `
})
export class AuthLayoutComponent { }
```

### 3. BlankLayoutComponent - 空白布局

#### 设计特点
- **最小化设计** - 只包含必要的容器元素
- **全屏显示** - 适用于特殊页面或全屏应用
- **无装饰** - 不包含导航、头部、页脚等元素

#### 核心实现
```typescript
@Component({
  selector: 'app-blank-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet
  ],
  template: `
    <div class="blank-layout">
      <router-outlet></router-outlet>
    </div>
  `,
  styles: [`
    .blank-layout {
      min-height: 100vh;
      width: 100%;
    }
  `]
})
export class BlankLayoutComponent {}
```

## 布局配置和切换

### 路由级布局配置

在路由配置中指定布局类型：

```typescript
export const routes: Routes = [
  // 默认后台管理布局
  {
    path: 'view',
    component: DefaultLayoutComponent,
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true
    },
    children: [
      // 子路由配置
    ]
  },
  
  // 认证布局
  {
    path: 'auth',
    component: AuthLayoutComponent,
    data: {
      layout: LayoutType.AUTH,
      requireAuth: false
    },
    children: [
      // 认证相关路由
    ]
  }
];
```

### 动态布局切换

```typescript
// 在组件中动态切换布局
constructor(private layoutService: LayoutService) {}

switchToAuthLayout(): void {
  this.layoutService.setLayout(LayoutType.AUTH);
}

switchToDefaultLayout(): void {
  this.layoutService.setLayout(LayoutType.DEFAULT, {
    showSidebar: true,
    sidebarCollapsed: false
  });
}
```

## 响应式设计

### 断点配置

```typescript
// 响应式断点
private readonly breakpoint = 768;

@HostListener('window:resize', ['$event'])
onResize(event: any): void {
  const width = event.target.innerWidth;
  if (width < this.breakpoint) {
    // 移动端处理
    this.isCollapsed.set(true);
  } else {
    // 桌面端处理
    this.isCollapsed.set(false);
  }
}
```

### CSS 媒体查询

```css
/* 桌面端样式 */
@media (min-width: 768px) {
  .layout-sider {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
  }
  
  .layout-content {
    margin-left: 200px;
  }
}

/* 移动端样式 */
@media (max-width: 767px) {
  .auth-container {
    flex-direction: column;
    max-width: 400px;
  }
  
  .auth-background {
    min-height: 200px;
  }
  
  .layout-sider {
    position: fixed;
    z-index: 1000;
  }
}
```

## 使用示例

### 基本使用

```typescript
// 在组件中使用布局服务
@Component({
  selector: 'app-example',
  template: `
    <div>
      <p>当前布局: {{ currentLayout() }}</p>
      <button (click)="toggleSidebar()">切换侧边栏</button>
    </div>
  `
})
export class ExampleComponent {
  private readonly layoutService = inject(LayoutService);
  
  readonly currentLayout = this.layoutService.currentLayout;
  readonly layoutConfig = this.layoutService.layoutConfig;
  
  toggleSidebar(): void {
    const config = this.layoutConfig();
    this.layoutService.updateLayoutConfig({
      sidebarCollapsed: !config.sidebarCollapsed
    });
  }
}
```

### 自定义布局组件

```typescript
// 1. 创建自定义布局组件
@Component({
  selector: 'app-custom-layout',
  standalone: true,
  template: `
    <div class="custom-layout">
      <header>自定义头部</header>
      <main>
        <router-outlet></router-outlet>
      </main>
      <footer>自定义页脚</footer>
    </div>
  `
})
export class CustomLayoutComponent {}

// 2. 注册自定义布局
export enum CustomLayoutType {
  CUSTOM = 'custom'
}

// 3. 在应用初始化时注册
constructor(private layoutService: LayoutService) {
  this.layoutService.registerLayoutComponent(
    CustomLayoutType.CUSTOM as any,
    CustomLayoutComponent
  );
}
```

## 最佳实践

### 1. 布局选择原则

- **后台管理页面** - 使用 DefaultLayoutComponent
- **认证相关页面** - 使用 AuthLayoutComponent  
- **特殊全屏页面** - 使用 BlankLayoutComponent
- **自定义需求** - 创建并注册自定义布局组件

### 2. 性能优化

```typescript
// 使用 OnPush 策略优化性能
@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  // ...
})
export class LayoutComponent {
  // 使用 Signal 自动处理变更检测
  readonly isCollapsed = signal(false);
  readonly menuItems = computed(() => this.getMenuItems());
}
```

### 3. 可访问性支持

```html
<!-- 添加适当的 ARIA 标签 -->
<nz-sider 
  role="navigation"
  aria-label="主导航菜单"
  [attr.aria-expanded]="!isCollapsed()">
  
<button 
  (click)="toggleSidebar()"
  [attr.aria-label]="isCollapsed() ? '展开菜单' : '收起菜单'">
  <nz-icon [nzType]="isCollapsed() ? 'menu-unfold' : 'menu-fold'"></nz-icon>
</button>
```

### 4. 主题定制

```typescript
// 在 app.config.ts 中配置主题
export const appConfig: ApplicationConfig = {
  providers: [
    provideNzConfig({
      theme: {
        primaryColor: '#1890ff'
      }
    })
  ]
};
```

---

## 总结

布局系统是企业级应用的重要组成部分，本项目实现了：

1. **灵活的多布局架构** - 支持不同场景的布局需求
2. **统一的管理界面** - admin 和 user 使用相同的后台布局
3. **响应式设计** - 完美适配各种设备尺寸
4. **可扩展性** - 支持自定义布局组件注册
5. **现代化技术栈** - 基于 Angular Signals 和 NG-ZORRO

通过合理使用布局系统，可以为用户提供一致、美观、易用的界面体验。
