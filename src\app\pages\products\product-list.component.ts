import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzGridModule } from 'ng-zorro-antd/grid';

/**
 * 产品列表组件（动态加载）
 * 
 * 这是一个动态加载的组件，用于演示动态路由功能
 * 显示产品列表和相关管理功能
 */
@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    NzGridModule
  ],
  template: `
    <div class="product-list-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="shopping" nzTheme="outline"></nz-icon>
          产品列表
        </h1>
        <p class="page-description">
          <nz-tag nzColor="processing">动态加载组件</nz-tag>
          管理和查看所有产品信息
        </p>
      </div>

      <!-- 操作按钮 -->
      <nz-card class="action-card">
        <div class="action-buttons">
          <button nz-button nzType="primary">
            <nz-icon nzType="plus"></nz-icon>
            添加产品
          </button>
          <button nz-button>
            <nz-icon nzType="reload"></nz-icon>
            刷新
          </button>
          <button nz-button>
            <nz-icon nzType="download"></nz-icon>
            导出
          </button>
        </div>
      </nz-card>

      <!-- 产品表格 -->
      <nz-card nzTitle="产品信息" class="table-card">
        <nz-table #productTable [nzData]="productData" [nzPageSize]="10">
          <thead>
            <tr>
              <th>产品ID</th>
              <th>产品名称</th>
              <th>分类</th>
              <th>价格</th>
              <th>库存</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let product of productTable.data">
              <td>{{ product.id }}</td>
              <td>{{ product.name }}</td>
              <td>
                <nz-tag nzColor="blue">{{ product.category }}</nz-tag>
              </td>
              <td>¥{{ product.price | number:'1.2-2' }}</td>
              <td>{{ product.stock }}</td>
              <td>
                <nz-tag [nzColor]="getStatusColor(product.status)">
                  {{ product.statusText }}
                </nz-tag>
              </td>
              <td>
                <button nz-button nzType="link" nzSize="small">
                  <nz-icon nzType="edit"></nz-icon>
                  编辑
                </button>
                <button nz-button nzType="link" nzSize="small" nzDanger>
                  <nz-icon nzType="delete"></nz-icon>
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 功能说明 -->
      <nz-card nzTitle="功能说明" class="info-card">
        <nz-row [nzGutter]="16">
          <nz-col [nzSpan]="12">
            <h4>🎯 组件特点</h4>
            <ul>
              <li>动态路由加载的组件</li>
              <li>完整的产品管理功能</li>
              <li>响应式表格设计</li>
              <li>支持增删改查操作</li>
            </ul>
          </nz-col>
          <nz-col [nzSpan]="12">
            <h4>🔧 技术实现</h4>
            <ul>
              <li>Standalone Component 架构</li>
              <li>NG-ZORRO 组件库</li>
              <li>懒加载 (loadComponent)</li>
              <li>TypeScript 类型安全</li>
            </ul>
          </nz-col>
        </nz-row>
      </nz-card>
    </div>
  `,
  styles: [`
    .product-list-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .action-card {
      margin-bottom: 16px;
    }

    .action-buttons button {
      margin-right: 8px;
    }

    .table-card {
      margin-bottom: 16px;
    }

    .info-card h4 {
      color: #262626;
      margin: 0 0 12px 0;
    }

    .info-card ul {
      margin: 0;
      padding-left: 20px;
    }

    .info-card li {
      margin: 4px 0;
      color: #595959;
    }
  `]
})
export class ProductListComponent {
  /**
   * 产品数据模拟
   */
  productData = [
    {
      id: 'P001',
      name: '笔记本电脑',
      category: '电子产品',
      price: 5999.00,
      stock: 50,
      status: 'active',
      statusText: '在售'
    },
    {
      id: 'P002',
      name: '智能手机',
      category: '电子产品',
      price: 3299.00,
      stock: 120,
      status: 'active',
      statusText: '在售'
    },
    {
      id: 'P003',
      name: '办公椅',
      category: '办公用品',
      price: 899.00,
      stock: 30,
      status: 'active',
      statusText: '在售'
    },
    {
      id: 'P004',
      name: '蓝牙耳机',
      category: '电子产品',
      price: 299.00,
      stock: 0,
      status: 'out_of_stock',
      statusText: '缺货'
    },
    {
      id: 'P005',
      name: '咖啡机',
      category: '家电',
      price: 1299.00,
      stock: 15,
      status: 'discontinued',
      statusText: '停产'
    }
  ];

  /**
   * 根据状态获取标签颜色
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'out_of_stock':
        return 'warning';
      case 'discontinued':
        return 'error';
      default:
        return 'default';
    }
  }
}
