import { Injectable, inject, signal, computed } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import { MockApiService } from './mock-api.service';
import { DynamicRouteService } from './dynamic-route.service';
import { LoadingService } from './loading.service';
import { ErrorService } from './error.service';

import { UserInfo } from '../types';
import { DEFAULT_ROUTES, CACHE_CONFIG } from '../constants';

/**
 * 认证服务
 *
 * 负责管理用户的认证状态、登录登出、权限检查等核心认证功能。
 * 采用现代化的 Angular Signal 和传统 Observable 双重状态管理模式。
 *
 * @description
 * 核心功能包括：
 * - 用户登录和登出
 * - 认证状态管理和持久化
 * - 权限和角色检查
 * - 令牌管理和自动刷新
 * - 多标签页状态同步
 *
 * 设计特点：
 * - 使用 Signal 提供响应式状态管理
 * - 使用 BehaviorSubject 支持传统 Observable 模式
 * - 支持本地缓存和自动恢复
 * - 提供细粒度的权限控制API
 *
 * @example
 * ```typescript
 * // 在组件中使用
 * constructor(private authService: AuthService) {}
 *
 * // 检查认证状态
 * const isLoggedIn = this.authService.isAuthenticated();
 *
 * // 检查权限
 * const canEdit = this.authService.hasPermission('user:edit');
 *
 * // 登录
 * this.authService.login({ username, password }).subscribe(user => {
 *   console.log('登录成功', user);
 * });
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class AuthService {
  /** Mock API 服务实例，用于模拟后端认证接口 */
  private readonly mockApi = inject(MockApiService);

  /** 路由服务实例，用于登录登出时的页面跳转 */
  private readonly router = inject(Router);

  /** 动态路由服务实例，用于在登录成功后加载动态路由配置 */
  private readonly dynamicRouteService = inject(DynamicRouteService);

  /** 加载状态服务实例，用于管理动态路由加载的状态提示 */
  private readonly loadingService = inject(LoadingService);

  /** 错误服务实例，用于处理动态路由加载失败的错误 */
  private readonly errorService = inject(ErrorService);

  // ==================== 状态管理 ====================

  /**
   * 用户信息的 BehaviorSubject
   *
   * 使用 BehaviorSubject 来管理用户状态，支持：
   * - 立即获取当前值
   * - 订阅状态变化
   * - 与传统 Observable 模式兼容
   *
   * @private
   */
  private readonly userInfoSubject = new BehaviorSubject<UserInfo | null>(null);

  /**
   * 用户信息的 Signal
   *
   * 使用 Angular Signal 提供现代化的响应式状态管理：
   * - 自动依赖追踪
   * - 更好的性能优化
   * - 类型安全的状态访问
   *
   * @private
   */
  private readonly userInfoSignal = signal<UserInfo | null>(null);

  // ==================== 公开状态接口 ====================

  /**
   * 用户信息的 Observable 流
   *
   * 提供传统的 Observable 接口，用于：
   * - 在服务间进行状态通信
   * - 与 RxJS 操作符配合使用
   * - 支持异步管道等传统模式
   *
   * @readonly
   */
  readonly userInfo$ = this.userInfoSubject.asObservable();

  /**
   * 用户信息的 Signal（只读）
   *
   * 提供现代化的 Signal 接口，用于：
   * - 在组件模板中直接使用
   * - 与计算属性配合使用
   * - 获得更好的性能和类型安全
   *
   * @readonly
   */
  readonly userInfo = this.userInfoSignal.asReadonly();

  // ==================== 计算属性 ====================

  /**
   * 用户认证状态的计算属性
   *
   * 基于用户信息自动计算认证状态，当用户信息变化时自动更新。
   *
   * @readonly
   * @returns boolean - 用户是否已认证
   *
   * @example
   * ```typescript
   * // 在组件中使用
   * readonly isLoggedIn = this.authService.isAuthenticated;
   *
   * // 在模板中使用
   * @if (authService.isAuthenticated()) {
   *   <p>欢迎回来！</p>
   * }
   * ```
   */
  readonly isAuthenticated = computed(() => {
    const user = this.userInfo();
    return user?.authenticated ?? false;
  });

  /**
   * 用户权限列表的计算属性
   *
   * 从用户信息中提取权限列表，用于权限检查。
   *
   * @readonly
   * @returns string[] - 用户拥有的权限代码列表
   *
   * @example
   * ```typescript
   * // 获取用户所有权限
   * const permissions = this.authService.userPermissions();
   * console.log('用户权限:', permissions);
   * ```
   */
  readonly userPermissions = computed(() => {
    const user = this.userInfo();
    return user?.permissions ?? [];
  });

  /**
   * 用户角色列表的计算属性
   *
   * 从用户信息中提取角色列表，用于基于角色的访问控制。
   *
   * @readonly
   * @returns Role[] - 用户拥有的角色列表
   *
   * @example
   * ```typescript
   * // 获取用户所有角色
   * const roles = this.authService.userRoles();
   * console.log('用户角色:', roles.map(r => r.name));
   * ```
   */
  readonly userRoles = computed(() => {
    const user = this.userInfo();
    return user?.roles ?? [];
  });

  /**
   * 构造函数
   *
   * 初始化认证服务，自动加载缓存的用户信息以恢复用户会话。
   *
   * @description
   * 初始化流程：
   * 1. 从本地存储加载缓存的用户信息
   * 2. 验证缓存的有效性（检查过期时间）
   * 3. 如果缓存有效，恢复用户认证状态
   * 4. 如果缓存无效或不存在，保持未认证状态
   */
  constructor() {
    this.loadCachedUserInfo();
  }

  // ==================== 核心认证方法 ====================

  /**
   * 用户登录
   *
   * 使用用户名和密码进行登录认证，成功后自动保存用户状态、缓存并加载动态路由配置。
   *
   * @param credentials - 登录凭据对象
   * @param credentials.username - 用户名
   * @param credentials.password - 密码
   * @returns Observable<UserInfo> - 返回用户信息的Observable
   *
   * @example
   * ```typescript
   * // 基本登录
   * this.authService.login({ username: 'admin', password: 'password' })
   *   .subscribe({
   *     next: (user) => {
   *       console.log('登录成功:', user.displayName);
   *       this.router.navigate(['/dashboard']);
   *     },
   *     error: (error) => {
   *       console.error('登录失败:', error.message);
   *     }
   *   });
   * ```
   *
   * @description
   * 登录流程：
   * 1. 调用 API 进行用户认证
   * 2. 登录成功后更新本地认证状态
   * 3. 缓存用户信息到本地存储
   * 4. 启动动态路由配置加载
   * 5. 返回用户信息给调用方
   *
   * @throws {Error} 当用户名或密码错误时抛出错误
   * @throws {Error} 当网络请求失败时抛出错误
   */
  login(credentials: { username: string; password: string }): Observable<UserInfo> {
    return this.mockApi.login(credentials).pipe(
      tap(userInfo => {
        // 登录成功后立即更新本地状态
        this.setUserInfo(userInfo);
        // 将用户信息缓存到本地存储，支持页面刷新后恢复状态
        this.cacheUserInfo(userInfo);

        // 登录成功后立即开始加载动态路由配置
        this.loadDynamicRoutesAfterLogin();
      }),
      catchError(error => {
        console.error('登录失败:', error);
        // 重新抛出错误，让调用方处理
        return throwError(() => error);
      })
    );
  }

  /**
   * 用户登出
   *
   * 清除用户认证状态、本地缓存，并重定向到登录页面。
   *
   * @returns Observable<boolean> - 返回登出操作结果的Observable
   *
   * @example
   * ```typescript
   * // 基本登出
   * this.authService.logout().subscribe(() => {
   *   console.log('已成功登出');
   * });
   *
   * // 在组件中使用
   * onLogout() {
   *   this.authService.logout().subscribe({
   *     next: () => {
   *       this.showMessage('已安全退出');
   *     }
   *   });
   * }
   * ```
   *
   * @description
   * 登出操作包括：
   * 1. 清除内存中的用户状态
   * 2. 删除本地存储的缓存数据
   * 3. 重定向到登录页面
   * 4. 触发状态变更通知
   */
  logout(): Observable<boolean> {
    // 清除本地状态并重定向
    this.clearUserInfo();
    this.router.navigate([DEFAULT_ROUTES.LOGIN]);
    return of(true);
  }

  /**
   * 获取用户信息
   *
   * 从服务端获取最新的用户信息，并更新本地状态和缓存。
   *
   * @returns Observable<UserInfo> - 返回用户信息的Observable
   *
   * @example
   * ```typescript
   * // 刷新用户信息
   * this.authService.getUserInfo().subscribe({
   *   next: (user) => {
   *     console.log('用户信息已更新:', user);
   *   },
   *   error: (error) => {
   *     console.error('获取用户信息失败:', error);
   *   }
   * });
   * ```
   *
   * @throws {Error} 当用户未认证时抛出错误
   * @throws {Error} 当网络请求失败时抛出错误
   */
  getUserInfo(): Observable<UserInfo> {
    return this.mockApi.getUserInfo().pipe(
      tap(userInfo => {
        // 更新本地状态
        this.setUserInfo(userInfo);
        // 更新缓存
        this.cacheUserInfo(userInfo);
      })
    );
  }

  // ==================== 权限检查方法 ====================

  /**
   * 检查用户是否有指定权限
   *
   * 检查当前用户是否拥有特定的功能权限。
   *
   * @param permission - 权限代码，如 'user:create', 'report:view'
   * @returns boolean - 用户是否拥有该权限
   *
   * @example
   * ```typescript
   * // 在组件中检查权限
   * canCreateUser(): boolean {
   *   return this.authService.hasPermission('user:create');
   * }
   *
   * // 在模板中使用
   * @if (authService.hasPermission('user:edit')) {
   *   <button (click)="editUser()">编辑用户</button>
   * }
   *
   * // 在服务中使用
   * performAction() {
   *   if (!this.authService.hasPermission('action:execute')) {
   *     throw new Error('权限不足');
   *   }
   *   // 执行操作...
   * }
   * ```
   */
  hasPermission(permission: string): boolean {
    const permissions = this.userPermissions();
    return permissions.includes(permission);
  }

  /**
   * 检查用户是否有任一指定权限
   *
   * 检查用户是否拥有权限列表中的任意一个权限（OR 逻辑）。
   *
   * @param permissions - 权限代码数组
   * @returns boolean - 用户是否拥有任一权限
   *
   * @example
   * ```typescript
   * // 检查用户是否有查看或编辑权限
   * canAccessUserData(): boolean {
   *   return this.authService.hasAnyPermission(['user:view', 'user:edit']);
   * }
   *
   * // 在路由守卫中使用
   * canActivate(): boolean {
   *   return this.authService.hasAnyPermission([
   *     'admin:access',
   *     'manager:access'
   *   ]);
   * }
   * ```
   */
  hasAnyPermission(permissions: string[]): boolean {
    const userPermissions = this.userPermissions();
    return permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * 检查用户是否有所有指定权限
   *
   * 检查用户是否拥有权限列表中的所有权限（AND 逻辑）。
   *
   * @param permissions - 权限代码数组
   * @returns boolean - 用户是否拥有所有权限
   *
   * @example
   * ```typescript
   * // 检查用户是否同时拥有查看和编辑权限
   * canFullyManageUsers(): boolean {
   *   return this.authService.hasAllPermissions([
   *     'user:view',
   *     'user:edit',
   *     'user:delete'
   *   ]);
   * }
   *
   * // 复杂权限检查
   * canPerformComplexOperation(): boolean {
   *   return this.authService.hasAllPermissions([
   *     'data:read',
   *     'data:write',
   *     'report:generate'
   *   ]);
   * }
   * ```
   */
  hasAllPermissions(permissions: string[]): boolean {
    const userPermissions = this.userPermissions();
    return permissions.every(permission => userPermissions.includes(permission));
  }

  /**
   * 检查用户是否有指定角色
   *
   * 检查当前用户是否拥有特定的角色。
   *
   * @param roleName - 角色名称，如 'admin', 'manager', 'user'
   * @returns boolean - 用户是否拥有该角色
   *
   * @example
   * ```typescript
   * // 检查是否是管理员
   * isAdmin(): boolean {
   *   return this.authService.hasRole('admin');
   * }
   *
   * // 在组件中使用
   * ngOnInit() {
   *   if (this.authService.hasRole('manager')) {
   *     this.loadManagerData();
   *   }
   * }
   *
   * // 条件渲染
   * @if (authService.hasRole('admin')) {
   *   <app-admin-panel></app-admin-panel>
   * }
   * ```
   */
  hasRole(roleName: string): boolean {
    const roles = this.userRoles();
    return roles.some(role => role.name === roleName);
  }

  /**
   * 检查用户是否有任一指定角色
   *
   * 检查用户是否拥有角色列表中的任意一个角色（OR 逻辑）。
   *
   * @param roleNames - 角色名称数组
   * @returns boolean - 用户是否拥有任一角色
   *
   * @example
   * ```typescript
   * // 检查用户是否是管理员或经理
   * canAccessManagement(): boolean {
   *   return this.authService.hasAnyRole(['admin', 'manager']);
   * }
   *
   * // 在路由配置中使用
   * {
   *   path: 'management',
   *   component: ManagementComponent,
   *   canActivate: [PermissionGuard],
   *   data: {
   *     roles: ['admin', 'manager', 'supervisor']
   *   }
   * }
   * ```
   */
  hasAnyRole(roleNames: string[]): boolean {
    const roles = this.userRoles();
    return roleNames.some(roleName =>
      roles.some(role => role.name === roleName)
    );
  }

  /**
   * 刷新令牌
   */
  refreshToken(): Observable<boolean> {
    // 在模拟环境中，直接返回成功
    return of(true);
  }

  /**
   * 检查认证状态
   */
  checkAuthStatus(): Observable<boolean> {
    const currentUser = this.userInfo();
    if (!currentUser?.authenticated) {
      return of(false);
    }

    // 验证令牌是否仍然有效
    return this.getUserInfo().pipe(
      map(() => true),
      catchError(() => {
        this.clearUserInfo();
        return of(false);
      })
    );
  }

  // 私有方法

  private setUserInfo(userInfo: UserInfo): void {
    this.userInfoSignal.set(userInfo);
    this.userInfoSubject.next(userInfo);
  }

  private clearUserInfo(): void {
    this.userInfoSignal.set(null);
    this.userInfoSubject.next(null);
    this.clearCachedUserInfo();
  }

  private cacheUserInfo(userInfo: UserInfo): void {
    try {
      const cacheData = {
        userInfo,
        timestamp: Date.now()
      };
      localStorage.setItem(
        CACHE_CONFIG.USER_INFO_KEY,
        JSON.stringify(cacheData)
      );
    } catch (error) {
      console.warn('缓存用户信息失败:', error);
    }
  }

  private loadCachedUserInfo(): void {
    try {
      const cachedData = localStorage.getItem(CACHE_CONFIG.USER_INFO_KEY);
      if (!cachedData) return;

      const { userInfo, timestamp } = JSON.parse(cachedData);
      const now = Date.now();

      // 检查缓存是否过期
      if (now - timestamp > CACHE_CONFIG.CACHE_EXPIRE_TIME) {
        this.clearCachedUserInfo();
        return;
      }

      // 恢复用户状态
      this.setUserInfo(userInfo);

      // 如果用户状态恢复成功且已认证，则加载动态路由配置
      // 这确保了在认证有效期内重新打开页面时也能正确加载动态路由
      if (userInfo.authenticated) {
        this.loadDynamicRoutesAfterLogin();
      }
    } catch (error) {
      console.warn('加载缓存用户信息失败:', error);
      this.clearCachedUserInfo();
    }
  }

  private clearCachedUserInfo(): void {
    localStorage.removeItem(CACHE_CONFIG.USER_INFO_KEY);
    localStorage.removeItem(CACHE_CONFIG.PERMISSIONS_KEY);
  }

  /**
   * 登录成功后加载动态路由配置
   *
   * 在用户登录成功后立即触发动态路由配置的加载，
   * 确保用户看到"正在加载路由配置..."提示时已经是登录状态。
   * 支持智能检查，避免重复加载已存在的路由配置。
   *
   * @private
   * @returns void
   *
   * @description
   * 加载流程：
   * 1. 检查是否已有路由配置或正在加载中
   * 2. 如果需要加载，显示"正在加载路由配置..."加载提示
   * 3. 调用动态路由服务从API获取路由配置
   * 4. 加载成功后关闭加载提示
   * 5. 加载失败时关闭加载提示并显示错误信息
   *
   * 这个方法确保了动态路由加载只在用户登录成功后执行，
   * 避免了在应用启动时就显示路由加载提示的问题。
   * 同时避免了重复加载已存在的路由配置。
   */
  private loadDynamicRoutesAfterLogin(): void {
    // 检查是否已经有路由配置
    const currentRoutes = this.dynamicRouteService.routeConfigs();
    console.log('当前路由配置:', currentRoutes);
    const isLoading = this.dynamicRouteService.isLoading();

    // 如果已经有路由配置或正在加载中，则跳过
    if (currentRoutes.length > 0 || isLoading) {
      console.log('动态路由已存在或正在加载中，跳过重复加载');
      return;
    }

    // 开始显示动态路由加载提示
    this.loadingService.startLoading('dynamic-routes', '正在加载路由配置...');

    // 从API加载动态路由配置
    this.dynamicRouteService.loadRoutesFromAPI().subscribe({
      next: (routes) => {
        console.log('登录后动态路由加载成功:', routes);
        // 关闭加载提示
        this.loadingService.stopLoading('dynamic-routes');
      },
      error: (error) => {
        console.error('登录后动态路由加载失败:', error);
        // 关闭加载提示
        this.loadingService.stopLoading('dynamic-routes');
        // 显示错误信息
        this.errorService.addError(
          'dynamic-routes-error',
          '动态路由加载失败',
          error.message || '无法加载路由配置，请刷新页面重试',
          'error'
        );
      }
    });
  }
}
