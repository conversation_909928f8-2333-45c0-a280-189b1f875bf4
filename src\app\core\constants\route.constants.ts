import { LayoutType } from '../types';

/**
 * 默认路由路径
 */
export const DEFAULT_ROUTES = {
  LOGIN: '/auth/login',
  DASHBOARD: '/dashboard',
  ADMIN_DASHBOARD: '/admin/dashboard',
  USER_DASHBOARD: '/user/dashboard',
  FORBIDDEN: '/403',
  NOT_FOUND: '/404',
  SERVER_ERROR: '/500'
} as const;

/**
 * 布局路由映射
 */
export const LAYOUT_ROUTE_MAPPING = {
  [LayoutType.DEFAULT]: '/default',
  [LayoutType.ADMIN]: '/admin',
  [LayoutType.USER]: '/user',
  [LayoutType.AUTH]: '/auth',
  [LayoutType.BLANK]: '/blank',
  [LayoutType.FULL]: '/'
} as const;

/**
 * 默认权限代码
 */
export const DEFAULT_PERMISSIONS = {
  // 系统管理权限
  SYSTEM_ADMIN: 'system:admin',
  SYSTEM_CONFIG: 'system:config',
  
  // 用户管理权限
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // 角色管理权限
  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  
  // 菜单管理权限
  MENU_VIEW: 'menu:view',
  MENU_CREATE: 'menu:create',
  MENU_UPDATE: 'menu:update',
  MENU_DELETE: 'menu:delete'
} as const;

/**
 * 默认角色
 */
export const DEFAULT_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
} as const;

/**
 * 路由加载配置
 */
export const ROUTE_LOADING_CONFIG = {
  /** 默认超时时间（毫秒） */
  DEFAULT_TIMEOUT: 10000,
  /** 重试次数 */
  RETRY_COUNT: 3,
  /** 重试延迟（毫秒） */
  RETRY_DELAY: 1000
} as const;

/**
 * 缓存配置
 */
export const CACHE_CONFIG = {
  /** 路由配置缓存键 */
  ROUTE_CONFIG_KEY: 'dynamic_route_config',
  /** 用户信息缓存键 */
  USER_INFO_KEY: 'user_info',
  /** 权限信息缓存键 */
  PERMISSIONS_KEY: 'user_permissions',
  /** 缓存过期时间（毫秒） */
  CACHE_EXPIRE_TIME: 30 * 60 * 1000 // 30分钟
} as const;

/**
 * API 端点
 */
export const API_ENDPOINTS = {
  /** 获取路由配置 */
  GET_ROUTES: '/api/routes',
  /** 获取用户信息 */
  GET_USER_INFO: '/api/user/info',
  /** 获取用户权限 */
  GET_USER_PERMISSIONS: '/api/user/permissions',
  /** 登录 */
  LOGIN: '/api/auth/login',
  /** 登出 */
  LOGOUT: '/api/auth/logout',
  /** 刷新令牌 */
  REFRESH_TOKEN: '/api/auth/refresh'
} as const;
