/**
 * 动态组件映射配置
 * 
 * 将后台返回的组件名称字符串映射到实际的懒加载组件
 * 这样可以根据字符串动态加载对应的组件
 */

/**
 * 组件映射类型定义
 */
export interface ComponentMapping {
  [componentName: string]: () => Promise<any>;
}

/**
 * 动态组件映射表
 * 
 * 键：后台返回的组件名称字符串
 * 值：对应的懒加载函数
 * 
 * @description
 * 当后台返回路由配置时，component 字段是字符串，
 * 我们需要根据这个字符串找到对应的组件进行懒加载。
 * 
 * @example
 * ```typescript
 * // 后台返回的路由配置
 * {
 *   path: 'products/list',
 *   component: 'ProductListComponent'  // 字符串
 * }
 * 
 * // 通过映射表找到对应的懒加载函数
 * const loadComponent = COMPONENT_MAPPING['ProductListComponent'];
 * ```
 */
export const COMPONENT_MAPPING: ComponentMapping = {
  // 产品管理相关组件
  'ProductListComponent': () => import('../../pages/products/product-list.component').then(m => m.ProductListComponent),
  'ProductCategoriesComponent': () => import('../../pages/products/product-categories.component').then(m => m.ProductCategoriesComponent),

  // 可以继续添加更多组件映射...
  // 'NewComponent': () => import('path/to/new-component').then(m => m.NewComponent),
};

/**
 * 根据组件名称获取懒加载函数
 * 
 * @param componentName 组件名称字符串
 * @returns 懒加载函数或 undefined
 * 
 * @example
 * ```typescript
 * const loadComponent = getComponentLoader('ProductListComponent');
 * if (loadComponent) {
 *   const component = await loadComponent();
 * }
 * ```
 */
export function getComponentLoader(componentName: string): (() => Promise<any>) | undefined {
  return COMPONENT_MAPPING[componentName];
}

/**
 * 检查组件是否已注册
 * 
 * @param componentName 组件名称字符串
 * @returns 是否已注册
 */
export function isComponentRegistered(componentName: string): boolean {
  return componentName in COMPONENT_MAPPING;
}

/**
 * 获取所有已注册的组件名称
 * 
 * @returns 组件名称数组
 */
export function getRegisteredComponentNames(): string[] {
  return Object.keys(COMPONENT_MAPPING);
}

/**
 * 动态注册新的组件映射
 * 
 * @param componentName 组件名称
 * @param loader 懒加载函数
 * 
 * @example
 * ```typescript
 * registerComponent('MyNewComponent', () => 
 *   import('./my-new-component').then(m => m.MyNewComponent)
 * );
 * ```
 */
export function registerComponent(
  componentName: string,
  loader: () => Promise<any>
): void {
  COMPONENT_MAPPING[componentName] = loader;
}

/**
 * 批量注册组件映射
 * 
 * @param mappings 组件映射对象
 * 
 * @example
 * ```typescript
 * registerComponents({
 *   'Component1': () => import('./component1').then(m => m.Component1),
 *   'Component2': () => import('./component2').then(m => m.Component2)
 * });
 * ```
 */
export function registerComponents(mappings: ComponentMapping): void {
  Object.assign(COMPONENT_MAPPING, mappings);
}

/**
 * 移除组件映射
 * 
 * @param componentName 组件名称
 */
export function unregisterComponent(componentName: string): void {
  delete COMPONENT_MAPPING[componentName];
}

/**
 * 清空所有组件映射
 */
export function clearComponentMappings(): void {
  Object.keys(COMPONENT_MAPPING).forEach(key => {
    delete COMPONENT_MAPPING[key];
  });
}

/**
 * 验证组件映射配置
 * 
 * @returns 验证结果
 */
export function validateComponentMappings(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查是否有空的映射
  Object.entries(COMPONENT_MAPPING).forEach(([name, loader]) => {
    if (!name || name.trim() === '') {
      errors.push('发现空的组件名称');
    }

    if (typeof loader !== 'function') {
      errors.push(`组件 "${name}" 的加载器不是函数`);
    }
  });

  // 检查命名规范
  Object.keys(COMPONENT_MAPPING).forEach(name => {
    if (!name.endsWith('Component')) {
      warnings.push(`组件名称 "${name}" 建议以 "Component" 结尾`);
    }

    if (!/^[A-Z]/.test(name)) {
      warnings.push(`组件名称 "${name}" 建议以大写字母开头`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
