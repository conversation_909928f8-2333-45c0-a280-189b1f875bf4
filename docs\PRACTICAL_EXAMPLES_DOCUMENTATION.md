# 实用示例文档

## 概述

本文档提供了 Angular 项目中各种实际使用场景的完整示例，包括路由配置、守卫使用、加载状态管理和认证集成的最佳实践。

## 目录

- [完整的企业级页面示例](#完整的企业级页面示例)
- [复杂路由配置示例](#复杂路由配置示例)
- [守卫组合使用示例](#守卫组合使用示例)
- [加载状态集成示例](#加载状态集成示例)
- [认证流程示例](#认证流程示例)
- [错误处理示例](#错误处理示例)
- [性能优化示例](#性能优化示例)

## 完整的企业级页面示例

### 用户管理页面

这是一个完整的用户管理页面示例，展示了如何集成认证、权限、加载状态和错误处理。

```typescript
// user-management.component.ts
import { Component, inject, signal, computed, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';

import { AuthService, LoadingService } from '../../core/services';
import { User, Role } from '../../core/types';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzPopconfirmModule
  ],
  template: `
    <div class="user-management">
      <div class="page-header">
        <h1>用户管理</h1>
        <div class="actions">
          <button 
            nz-button 
            nzType="primary" 
            (click)="showCreateModal()"
            *ngIf="canCreateUser()">
            <span nz-icon nzType="plus"></span>
            新增用户
          </button>
          <button 
            nz-button 
            (click)="refreshUsers()"
            [nzLoading]="isLoadingUsers()">
            <span nz-icon nzType="reload"></span>
            刷新
          </button>
        </div>
      </div>

      <nz-table 
        #userTable
        [nzData]="users()" 
        [nzLoading]="isLoadingUsers()"
        [nzPageSize]="pageSize"
        [nzShowPagination]="true">
        <thead>
          <tr>
            <th>用户名</th>
            <th>邮箱</th>
            <th>角色</th>
            <th>状态</th>
            <th>创建时间</th>
            <th nzWidth="200px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of userTable.data">
            <td>{{ user.username }}</td>
            <td>{{ user.email }}</td>
            <td>
              <nz-tag *ngFor="let role of user.roles" [nzColor]="getRoleColor(role.name)">
                {{ role.name }}
              </nz-tag>
            </td>
            <td>
              <nz-tag [nzColor]="user.active ? 'green' : 'red'">
                {{ user.active ? '激活' : '禁用' }}
              </nz-tag>
            </td>
            <td>{{ user.createdAt | date:'yyyy-MM-dd HH:mm' }}</td>
            <td>
              <nz-button-group>
                <button 
                  nz-button 
                  nzSize="small" 
                  (click)="editUser(user)"
                  *ngIf="canEditUser(user)">
                  编辑
                </button>
                <button 
                  nz-button 
                  nzSize="small" 
                  nzDanger
                  nz-popconfirm
                  nzPopconfirmTitle="确定要删除这个用户吗？"
                  (nzOnConfirm)="deleteUser(user.id)"
                  *ngIf="canDeleteUser(user)">
                  删除
                </button>
              </nz-button-group>
            </td>
          </tr>
        </tbody>
      </nz-table>

      <!-- 用户编辑模态框 -->
      <nz-modal
        [(nzVisible)]="isModalVisible()"
        [nzTitle]="modalTitle()"
        [nzOkText]="isEditing() ? '更新' : '创建'"
        [nzOkLoading]="isSubmitting()"
        (nzOnOk)="handleSubmit()"
        (nzOnCancel)="closeModal()">
        
        <ng-container *nzModalContent>
          <form nz-form [formGroup]="userForm" [nzLayout]="'vertical'">
            <nz-form-item>
              <nz-form-label nzRequired>用户名</nz-form-label>
              <nz-form-control nzErrorTip="请输入用户名">
                <input 
                  nz-input 
                  formControlName="username" 
                  placeholder="请输入用户名"
                  [readonly]="isEditing()">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label nzRequired>邮箱</nz-form-label>
              <nz-form-control nzErrorTip="请输入有效的邮箱地址">
                <input 
                  nz-input 
                  formControlName="email" 
                  placeholder="请输入邮箱">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item *ngIf="!isEditing()">
              <nz-form-label nzRequired>密码</nz-form-label>
              <nz-form-control nzErrorTip="密码至少6位">
                <input 
                  nz-input 
                  type="password"
                  formControlName="password" 
                  placeholder="请输入密码">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label>角色</nz-form-label>
              <nz-form-control>
                <nz-select 
                  formControlName="roleIds" 
                  nzMode="multiple"
                  nzPlaceHolder="请选择角色">
                  <nz-option 
                    *ngFor="let role of availableRoles()" 
                    [nzValue]="role.id" 
                    [nzLabel]="role.name">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </form>
        </ng-container>
      </nz-modal>
    </div>
  `,
  styles: [`
    .user-management {
      padding: 24px;
    }
    
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    
    .page-header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  `]
})
export class UserManagementComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly loadingService = inject(LoadingService);
  private readonly userService = inject(UserService);
  private readonly roleService = inject(RoleService);
  private readonly message = inject(NzMessageService);
  private readonly fb = inject(FormBuilder);

  // 状态管理
  private readonly usersSignal = signal<User[]>([]);
  private readonly availableRolesSignal = signal<Role[]>([]);
  private readonly isModalVisibleSignal = signal(false);
  private readonly editingUserSignal = signal<User | null>(null);

  // 表单
  userForm: FormGroup = this.fb.group({
    username: ['', [Validators.required]],
    email: ['', [Validators.required, Validators.email]],
    password: ['', [Validators.required, Validators.minLength(6)]],
    roleIds: [[], []]
  });

  // 分页
  pageSize = 10;

  // 计算属性
  readonly users = this.usersSignal.asReadonly();
  readonly availableRoles = this.availableRolesSignal.asReadonly();
  readonly isModalVisible = this.isModalVisibleSignal.asReadonly();
  readonly editingUser = this.editingUserSignal.asReadonly();
  
  readonly isEditing = computed(() => this.editingUser() !== null);
  readonly modalTitle = computed(() => this.isEditing() ? '编辑用户' : '新增用户');
  
  // 加载状态
  readonly isLoadingUsers = computed(() => this.loadingService.isLoading('load-users'));
  readonly isSubmitting = computed(() => this.loadingService.isLoading('submit-user'));

  ngOnInit(): void {
    this.loadUsers();
    this.loadRoles();
  }

  // 权限检查
  canCreateUser(): boolean {
    return this.authService.hasPermission('user:create');
  }

  canEditUser(user: User): boolean {
    return this.authService.hasPermission('user:edit') || 
           (this.authService.hasPermission('user:edit:own') && this.isOwnUser(user));
  }

  canDeleteUser(user: User): boolean {
    return this.authService.hasPermission('user:delete') && !this.isOwnUser(user);
  }

  private isOwnUser(user: User): boolean {
    const currentUser = this.authService.userInfo();
    return currentUser?.id === user.id;
  }

  // 数据加载
  loadUsers(): void {
    this.loadingService.startLoading('load-users', '正在加载用户列表...');
    
    this.userService.getUsers().subscribe({
      next: (users) => {
        this.usersSignal.set(users);
        this.loadingService.stopLoading('load-users');
      },
      error: (error) => {
        this.message.error('加载用户列表失败');
        this.loadingService.stopLoading('load-users');
      }
    });
  }

  loadRoles(): void {
    this.roleService.getRoles().subscribe({
      next: (roles) => {
        this.availableRolesSignal.set(roles);
      },
      error: (error) => {
        this.message.error('加载角色列表失败');
      }
    });
  }

  refreshUsers(): void {
    this.loadUsers();
  }

  // 模态框操作
  showCreateModal(): void {
    this.editingUserSignal.set(null);
    this.userForm.reset();
    this.userForm.get('password')?.setValidators([Validators.required, Validators.minLength(6)]);
    this.isModalVisibleSignal.set(true);
  }

  editUser(user: User): void {
    this.editingUserSignal.set(user);
    this.userForm.patchValue({
      username: user.username,
      email: user.email,
      roleIds: user.roles.map(role => role.id)
    });
    this.userForm.get('password')?.clearValidators();
    this.userForm.get('password')?.updateValueAndValidity();
    this.isModalVisibleSignal.set(true);
  }

  closeModal(): void {
    this.isModalVisibleSignal.set(false);
    this.editingUserSignal.set(null);
    this.userForm.reset();
  }

  handleSubmit(): void {
    if (this.userForm.valid) {
      const formValue = this.userForm.value;
      const operation = this.isEditing() ? '更新用户' : '创建用户';
      
      this.loadingService.startLoading('submit-user', `正在${operation}...`);

      const request = this.isEditing() 
        ? this.userService.updateUser(this.editingUser()!.id, formValue)
        : this.userService.createUser(formValue);

      request.subscribe({
        next: (user) => {
          this.loadingService.stopLoading('submit-user');
          this.message.success(`${operation}成功`);
          this.closeModal();
          this.loadUsers(); // 重新加载用户列表
        },
        error: (error) => {
          this.loadingService.stopLoading('submit-user');
          this.message.error(`${operation}失败: ${error.message}`);
        }
      });
    } else {
      // 标记所有字段为已触摸，显示验证错误
      Object.values(this.userForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  deleteUser(userId: string): void {
    this.loadingService.startLoading('delete-user', '正在删除用户...');
    
    this.userService.deleteUser(userId).subscribe({
      next: () => {
        this.loadingService.stopLoading('delete-user');
        this.message.success('用户删除成功');
        this.loadUsers(); // 重新加载用户列表
      },
      error: (error) => {
        this.loadingService.stopLoading('delete-user');
        this.message.error(`删除用户失败: ${error.message}`);
      }
    });
  }

  // 工具方法
  getRoleColor(roleName: string): string {
    const colorMap: Record<string, string> = {
      'admin': 'red',
      'manager': 'orange',
      'user': 'blue',
      'guest': 'default'
    };
    return colorMap[roleName] || 'default';
  }
}
```

## 复杂路由配置示例

### 多层嵌套路由配置

```typescript
// complex-routes.config.ts
import { Routes } from '@angular/router';
import { AuthGuard, PermissionGuard, AdminGuard } from '../core/guards';
import { LayoutType } from '../core/types';

export const complexRoutes: Routes = [
  // 根路由重定向
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },

  // 仪表盘路由
  {
    path: 'dashboard',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      breadcrumb: [
        { title: '首页', path: '/dashboard' }
      ]
    },
    children: [
      {
        path: '',
        component: DashboardComponent,
        data: { title: '仪表盘' }
      }
    ]
  },

  // 管理模块 - 需要管理员权限
  {
    path: 'admin',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    canActivateChild: [PermissionGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: ['admin', 'super_admin'],
      breadcrumb: [
        { title: '首页', path: '/dashboard' },
        { title: '系统管理', path: '/admin' }
      ]
    },
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        component: AdminOverviewComponent,
        data: {
          title: '管理概览',
          permissions: ['admin:view']
        }
      },
      {
        path: 'users',
        loadComponent: () => import('./admin/user-management.component')
          .then(m => m.UserManagementComponent),
        data: {
          title: '用户管理',
          permissions: ['user:view']
        }
      },
      {
        path: 'users/:id',
        loadComponent: () => import('./admin/user-detail.component')
          .then(m => m.UserDetailComponent),
        data: {
          title: '用户详情',
          permissions: ['user:view']
        }
      },
      {
        path: 'roles',
        loadComponent: () => import('./admin/role-management.component')
          .then(m => m.RoleManagementComponent),
        data: {
          title: '角色管理',
          permissions: ['role:view']
        }
      },
      {
        path: 'settings',
        data: {
          title: '系统设置',
          permissions: ['system:config']
        },
        children: [
          {
            path: '',
            redirectTo: 'general',
            pathMatch: 'full'
          },
          {
            path: 'general',
            loadComponent: () => import('./admin/settings/general-settings.component')
              .then(m => m.GeneralSettingsComponent),
            data: { title: '常规设置' }
          },
          {
            path: 'security',
            loadComponent: () => import('./admin/settings/security-settings.component')
              .then(m => m.SecuritySettingsComponent),
            data: {
              title: '安全设置',
              permissions: ['security:config']
            }
          }
        ]
      }
    ]
  },

  // 报表模块 - 条件权限
  {
    path: 'reports',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      permissions: ['report:view'],
      breadcrumb: [
        { title: '首页', path: '/dashboard' },
        { title: '报表中心', path: '/reports' }
      ]
    },
    children: [
      {
        path: '',
        component: ReportsOverviewComponent,
        data: { title: '报表概览' }
      },
      {
        path: 'sales',
        loadComponent: () => import('./reports/sales-report.component')
          .then(m => m.SalesReportComponent),
        data: {
          title: '销售报表',
          permissions: ['report:sales']
        }
      },
      {
        path: 'financial',
        loadComponent: () => import('./reports/financial-report.component')
          .then(m => m.FinancialReportComponent),
        data: {
          title: '财务报表',
          permissions: ['report:financial'],
          roles: ['admin', 'finance_manager']
        }
      }
    ]
  },

  // 用户个人中心
  {
    path: 'profile',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      breadcrumb: [
        { title: '首页', path: '/dashboard' },
        { title: '个人中心', path: '/profile' }
      ]
    },
    children: [
      {
        path: '',
        redirectTo: 'info',
        pathMatch: 'full'
      },
      {
        path: 'info',
        loadComponent: () => import('./profile/profile-info.component')
          .then(m => m.ProfileInfoComponent),
        data: { title: '个人信息' }
      },
      {
        path: 'security',
        loadComponent: () => import('./profile/profile-security.component')
          .then(m => m.ProfileSecurityComponent),
        data: { title: '安全设置' }
      },
      {
        path: 'preferences',
        loadComponent: () => import('./profile/profile-preferences.component')
          .then(m => m.ProfilePreferencesComponent),
        data: { title: '偏好设置' }
      }
    ]
  },

  // 认证路由
  {
    path: 'auth',
    component: AuthLayoutComponent,
    data: {
      layout: LayoutType.AUTH,
      requireAuth: false
    },
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        component: LoginComponent,
        data: { title: '用户登录' }
      },
      {
        path: 'register',
        loadComponent: () => import('./auth/register.component')
          .then(m => m.RegisterComponent),
        data: { title: '用户注册' }
      },
      {
        path: 'forgot-password',
        loadComponent: () => import('./auth/forgot-password.component')
          .then(m => m.ForgotPasswordComponent),
        data: { title: '忘记密码' }
      }
    ]
  },

  // 错误页面
  {
    path: 'error',
    component: BlankLayoutComponent,
    data: { layout: LayoutType.BLANK },
    children: [
      {
        path: '403',
        component: ForbiddenComponent,
        data: { title: '访问被拒绝' }
      },
      {
        path: '404',
        component: NotFoundComponent,
        data: { title: '页面不存在' }
      },
      {
        path: '500',
        loadComponent: () => import('./error/server-error.component')
          .then(m => m.ServerErrorComponent),
        data: { title: '服务器错误' }
      }
    ]
  },

  // 通配符路由
  {
    path: '**',
    redirectTo: '/error/404'
  }
];
```

### 动态路由注册示例

```typescript
// dynamic-route-registration.service.ts
@Injectable({
  providedIn: 'root'
})
export class DynamicRouteRegistrationService {
  private readonly router = inject(Router);
  private readonly dynamicRouteService = inject(DynamicRouteService);
  private readonly authService = inject(AuthService);

  /**
   * 根据用户权限动态注册路由
   */
  registerUserRoutes(): Observable<boolean> {
    const userInfo = this.authService.userInfo();
    if (!userInfo) {
      return of(false);
    }

    const routesToRegister: DynamicRouteConfig[] = [];

    // 根据用户角色添加不同的路由
    if (this.authService.hasRole('admin')) {
      routesToRegister.push(...this.getAdminRoutes());
    }

    if (this.authService.hasRole('manager')) {
      routesToRegister.push(...this.getManagerRoutes());
    }

    if (this.authService.hasPermission('report:view')) {
      routesToRegister.push(...this.getReportRoutes());
    }

    // 批量注册路由
    return this.batchRegisterRoutes(routesToRegister);
  }

  private getAdminRoutes(): DynamicRouteConfig[] {
    return [
      {
        id: 'system-monitor',
        path: 'admin/monitor',
        title: '系统监控',
        layout: LayoutType.DEFAULT,
        requireAuth: true,
        roles: ['admin'],
        loadComponent: () => import('./admin/system-monitor.component')
          .then(m => m.SystemMonitorComponent),
        menu: {
          id: 'system-monitor',
          title: '系统监控',
          icon: 'monitor',
          path: '/admin/monitor',
          order: 100
        }
      },
      {
        id: 'audit-logs',
        path: 'admin/audit',
        title: '审计日志',
        layout: LayoutType.DEFAULT,
        requireAuth: true,
        roles: ['admin'],
        permissions: ['audit:view'],
        loadComponent: () => import('./admin/audit-logs.component')
          .then(m => m.AuditLogsComponent),
        menu: {
          id: 'audit-logs',
          title: '审计日志',
          icon: 'file-text',
          path: '/admin/audit',
          order: 101
        }
      }
    ];
  }

  private getManagerRoutes(): DynamicRouteConfig[] {
    return [
      {
        id: 'team-management',
        path: 'manager/team',
        title: '团队管理',
        layout: LayoutType.DEFAULT,
        requireAuth: true,
        roles: ['manager'],
        loadComponent: () => import('./manager/team-management.component')
          .then(m => m.TeamManagementComponent),
        menu: {
          id: 'team-management',
          title: '团队管理',
          icon: 'team',
          path: '/manager/team',
          order: 200
        }
      }
    ];
  }

  private getReportRoutes(): DynamicRouteConfig[] {
    return [
      {
        id: 'advanced-reports',
        path: 'reports/advanced',
        title: '高级报表',
        layout: LayoutType.DEFAULT,
        requireAuth: true,
        permissions: ['report:advanced'],
        loadComponent: () => import('./reports/advanced-reports.component')
          .then(m => m.AdvancedReportsComponent),
        menu: {
          id: 'advanced-reports',
          title: '高级报表',
          icon: 'bar-chart',
          path: '/reports/advanced',
          order: 300
        }
      }
    ];
  }

  private batchRegisterRoutes(routes: DynamicRouteConfig[]): Observable<boolean> {
    const registrationObservables = routes.map(route =>
      this.dynamicRouteService.addRoute(route)
    );

    return forkJoin(registrationObservables).pipe(
      map(results => results.every(result => result)),
      catchError(error => {
        console.error('批量注册路由失败:', error);
        return of(false);
      })
    );
  }
}
```

## 守卫组合使用示例

### 复合守卫实现

```typescript
// composite-guard.service.ts
@Injectable({
  providedIn: 'root'
})
export class CompositeGuard implements CanActivate, CanActivateChild {
  private readonly authService = inject(AuthService);
  private readonly loadingService = inject(LoadingService);
  private readonly router = inject(Router);
  private readonly message = inject(NzMessageService);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return this.executeGuardChain(route, state);
  }

  canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return this.executeGuardChain(childRoute, state);
  }

  private executeGuardChain(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const guardKey = `guard-${Date.now()}`;
    this.loadingService.startLoading(guardKey, '验证访问权限...');

    return this.checkAuthentication(route, state).pipe(
      switchMap(authResult => {
        if (!authResult) {
          return of(false);
        }
        return this.checkPermissions(route, state);
      }),
      switchMap(permissionResult => {
        if (!permissionResult) {
          return of(false);
        }
        return this.checkBusinessRules(route, state);
      }),
      switchMap(businessResult => {
        if (!businessResult) {
          return of(false);
        }
        return this.checkTimeRestrictions(route, state);
      }),
      tap(finalResult => {
        this.loadingService.stopLoading(guardKey);
        if (!finalResult) {
          this.handleAccessDenied(route, state);
        }
      }),
      catchError(error => {
        this.loadingService.stopLoading(guardKey);
        console.error('守卫检查失败:', error);
        this.message.error('访问验证失败，请重试');
        return of(false);
      })
    );
  }

  private checkAuthentication(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const requireAuth = route.data?.['requireAuth'] ?? true;

    if (!requireAuth) {
      return of(true);
    }

    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    }

    return this.authService.checkAuthStatus();
  }

  private checkPermissions(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const requiredPermissions = route.data?.['permissions'] as string[] || [];
    const requiredRoles = route.data?.['roles'] as string[] || [];

    if (requiredPermissions.length === 0 && requiredRoles.length === 0) {
      return of(true);
    }

    const hasPermissions = requiredPermissions.length === 0 ||
      this.authService.hasAnyPermission(requiredPermissions);
    const hasRoles = requiredRoles.length === 0 ||
      this.authService.hasAnyRole(requiredRoles);

    return of(hasPermissions && hasRoles);
  }

  private checkBusinessRules(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const businessRules = route.data?.['businessRules'] as string[] || [];

    if (businessRules.length === 0) {
      return of(true);
    }

    // 检查业务规则
    const ruleChecks = businessRules.map(rule => this.evaluateBusinessRule(rule, route));

    return forkJoin(ruleChecks).pipe(
      map(results => results.every(result => result))
    );
  }

  private evaluateBusinessRule(rule: string, route: ActivatedRouteSnapshot): Observable<boolean> {
    switch (rule) {
      case 'user-profile-complete':
        return this.checkUserProfileComplete();
      case 'subscription-active':
        return this.checkSubscriptionActive();
      case 'trial-period-valid':
        return this.checkTrialPeriodValid();
      default:
        return of(true);
    }
  }

  private checkUserProfileComplete(): Observable<boolean> {
    const userInfo = this.authService.userInfo();
    return of(userInfo?.email && userInfo?.displayName ? true : false);
  }

  private checkSubscriptionActive(): Observable<boolean> {
    // 模拟订阅状态检查
    return of(true);
  }

  private checkTrialPeriodValid(): Observable<boolean> {
    // 模拟试用期检查
    return of(true);
  }

  private checkTimeRestrictions(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const timeRestrictions = route.data?.['timeRestrictions'];

    if (!timeRestrictions) {
      return of(true);
    }

    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // 检查工作时间限制
    if (timeRestrictions.businessHoursOnly) {
      const isBusinessHours = currentHour >= 9 && currentHour <= 17;
      const isWeekday = currentDay >= 1 && currentDay <= 5;

      if (!isBusinessHours || !isWeekday) {
        this.message.warning('此功能仅在工作时间（周一至周五 9:00-17:00）可用');
        return of(false);
      }
    }

    // 检查维护时间限制
    if (timeRestrictions.maintenanceWindow) {
      const maintenanceStart = timeRestrictions.maintenanceWindow.start;
      const maintenanceEnd = timeRestrictions.maintenanceWindow.end;

      if (currentHour >= maintenanceStart && currentHour <= maintenanceEnd) {
        this.message.warning('系统正在维护中，请稍后再试');
        return of(false);
      }
    }

    return of(true);
  }

  private handleAccessDenied(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): void {
    const errorType = route.data?.['errorType'] || 'permission';

    switch (errorType) {
      case 'authentication':
        this.router.navigate(['/auth/login'], {
          queryParams: { returnUrl: state.url }
        });
        break;
      case 'permission':
        this.router.navigate(['/error/403']);
        break;
      case 'business-rule':
        this.router.navigate(['/profile/complete']);
        break;
      default:
        this.router.navigate(['/error/403']);
    }
  }
}
```

### 路由配置中使用复合守卫

```typescript
// 使用复合守卫的路由配置
const protectedRoutes: Routes = [
  {
    path: 'premium-feature',
    component: PremiumFeatureComponent,
    canActivate: [CompositeGuard],
    data: {
      requireAuth: true,
      permissions: ['premium:access'],
      businessRules: ['subscription-active', 'user-profile-complete'],
      timeRestrictions: {
        businessHoursOnly: true
      },
      errorType: 'business-rule'
    }
  },
  {
    path: 'admin/maintenance',
    component: MaintenanceComponent,
    canActivate: [CompositeGuard],
    data: {
      requireAuth: true,
      roles: ['admin'],
      timeRestrictions: {
        maintenanceWindow: {
          start: 2,  // 凌晨2点
          end: 4     // 凌晨4点
        }
      }
    }
  }
];
```

## 加载状态集成示例

### 智能加载管理器

```typescript
// smart-loading-manager.service.ts
@Injectable({
  providedIn: 'root'
})
export class SmartLoadingManager {
  private readonly loadingService = inject(LoadingService);
  private loadingQueue = new Map<string, LoadingQueueItem>();
  private globalLoadingThreshold = 3; // 超过3个加载状态时显示全局加载

  interface LoadingQueueItem {
    key: string;
    priority: number;
    startTime: number;
    category: 'api' | 'ui' | 'background';
    timeout?: number;
  }

  /**
   * 智能开始加载
   */
  startSmartLoading(
    key: string,
    message: string,
    options: {
      priority?: number;
      category?: 'api' | 'ui' | 'background';
      timeout?: number;
      showGlobal?: boolean;
    } = {}
  ): void {
    const {
      priority = 1,
      category = 'api',
      timeout = 30000,
      showGlobal = false
    } = options;

    // 添加到队列
    this.loadingQueue.set(key, {
      key,
      priority,
      startTime: Date.now(),
      category,
      timeout
    });

    // 决定是否显示全局加载
    const shouldShowGlobal = showGlobal ||
      this.loadingQueue.size >= this.globalLoadingThreshold ||
      priority >= 5;

    if (shouldShowGlobal) {
      this.loadingService.startLoading('global-loading', this.getGlobalMessage());
    } else {
      this.loadingService.startLoading(key, message);
    }

    // 设置超时
    if (timeout > 0) {
      setTimeout(() => {
        if (this.loadingQueue.has(key)) {
          console.warn(`加载超时: ${key}`);
          this.stopSmartLoading(key);
        }
      }, timeout);
    }
  }

  /**
   * 智能停止加载
   */
  stopSmartLoading(key: string): void {
    this.loadingQueue.delete(key);
    this.loadingService.stopLoading(key);

    // 检查是否需要停止全局加载
    if (this.loadingQueue.size < this.globalLoadingThreshold) {
      this.loadingService.stopLoading('global-loading');
    } else {
      // 更新全局加载消息
      this.loadingService.startLoading('global-loading', this.getGlobalMessage());
    }
  }

  /**
   * 批量加载管理
   */
  manageBatchLoading<T>(
    operations: Array<{
      key: string;
      operation: Observable<T>;
      message: string;
      weight?: number;
    }>
  ): Observable<T[]> {
    const totalWeight = operations.reduce((sum, op) => sum + (op.weight || 1), 0);
    let completedWeight = 0;

    this.loadingService.startLoading('batch-operation', '批量处理中...', 0);

    const operationObservables = operations.map(({ key, operation, message, weight = 1 }) => {
      this.startSmartLoading(key, message, { category: 'background' });

      return operation.pipe(
        tap(() => {
          completedWeight += weight;
          const progress = Math.round((completedWeight / totalWeight) * 100);
          this.loadingService.updateProgress('batch-operation', progress,
            `批量处理中... ${progress}%`);
        }),
        finalize(() => {
          this.stopSmartLoading(key);
        })
      );
    });

    return forkJoin(operationObservables).pipe(
      finalize(() => {
        this.loadingService.stopLoading('batch-operation');
      })
    );
  }

  private getGlobalMessage(): string {
    const categories = Array.from(this.loadingQueue.values())
      .reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    if (categories.api > 0) {
      return `正在处理 ${categories.api} 个请求...`;
    } else if (categories.ui > 0) {
      return `正在加载界面...`;
    } else {
      return `正在处理后台任务...`;
    }
  }

  /**
   * 获取加载统计信息
   */
  getLoadingStats(): {
    total: number;
    byCategory: Record<string, number>;
    longestRunning: string | null;
  } {
    const byCategory = Array.from(this.loadingQueue.values())
      .reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const longestRunning = Array.from(this.loadingQueue.values())
      .sort((a, b) => a.startTime - b.startTime)[0]?.key || null;

    return {
      total: this.loadingQueue.size,
      byCategory,
      longestRunning
    };
  }
}
```

### 使用智能加载管理器的组件示例

```typescript
// data-processing.component.ts
@Component({
  selector: 'app-data-processing',
  template: `
    <div class="data-processing">
      <h2>数据处理中心</h2>

      <div class="actions">
        <button nz-button nzType="primary" (click)="processData()">
          处理数据
        </button>
        <button nz-button (click)="batchImport()">
          批量导入
        </button>
        <button nz-button (click)="generateReports()">
          生成报表
        </button>
      </div>

      <div class="loading-stats" *ngIf="loadingStats().total > 0">
        <nz-card nzTitle="加载状态统计">
          <p>总计: {{ loadingStats().total }} 个任务</p>
          <p>API 请求: {{ loadingStats().byCategory['api'] || 0 }}</p>
          <p>界面加载: {{ loadingStats().byCategory['ui'] || 0 }}</p>
          <p>后台任务: {{ loadingStats().byCategory['background'] || 0 }}</p>
          <p *ngIf="loadingStats().longestRunning">
            最长运行: {{ loadingStats().longestRunning }}
          </p>
        </nz-card>
      </div>
    </div>
  `
})
export class DataProcessingComponent {
  private readonly smartLoadingManager = inject(SmartLoadingManager);
  private readonly dataService = inject(DataService);

  loadingStats = computed(() => this.smartLoadingManager.getLoadingStats());

  processData(): void {
    this.smartLoadingManager.startSmartLoading(
      'process-data',
      '正在处理数据...',
      {
        priority: 3,
        category: 'api',
        timeout: 60000
      }
    );

    this.dataService.processData().subscribe({
      next: (result) => {
        console.log('数据处理完成:', result);
        this.smartLoadingManager.stopSmartLoading('process-data');
      },
      error: (error) => {
        console.error('数据处理失败:', error);
        this.smartLoadingManager.stopSmartLoading('process-data');
      }
    });
  }

  batchImport(): void {
    const operations = [
      {
        key: 'import-users',
        operation: this.dataService.importUsers(),
        message: '导入用户数据...',
        weight: 2
      },
      {
        key: 'import-products',
        operation: this.dataService.importProducts(),
        message: '导入产品数据...',
        weight: 3
      },
      {
        key: 'import-orders',
        operation: this.dataService.importOrders(),
        message: '导入订单数据...',
        weight: 1
      }
    ];

    this.smartLoadingManager.manageBatchLoading(operations).subscribe({
      next: (results) => {
        console.log('批量导入完成:', results);
      },
      error: (error) => {
        console.error('批量导入失败:', error);
      }
    });
  }

  generateReports(): void {
    this.smartLoadingManager.startSmartLoading(
      'generate-reports',
      '正在生成报表...',
      {
        priority: 5, // 高优先级，会显示全局加载
        category: 'background',
        timeout: 120000 // 2分钟超时
      }
    );

    this.dataService.generateReports().subscribe({
      next: (reports) => {
        console.log('报表生成完成:', reports);
        this.smartLoadingManager.stopSmartLoading('generate-reports');
      },
      error: (error) => {
        console.error('报表生成失败:', error);
        this.smartLoadingManager.stopSmartLoading('generate-reports');
      }
    });
  }
}
```

## 认证流程示例

### 完整的认证流程组件

```typescript
// auth-flow.component.ts
@Component({
  selector: 'app-auth-flow',
  template: `
    <div class="auth-flow">
      @switch (currentStep()) {
        @case ('login') {
          <app-login-step
            (onSuccess)="handleLoginSuccess($event)"
            (onError)="handleError($event)">
          </app-login-step>
        }
        @case ('mfa') {
          <app-mfa-step
            [token]="tempToken()"
            (onSuccess)="handleMfaSuccess($event)"
            (onError)="handleError($event)"
            (onBack)="goToStep('login')">
          </app-mfa-step>
        }
        @case ('profile-setup') {
          <app-profile-setup-step
            (onSuccess)="handleProfileSetupSuccess($event)"
            (onSkip)="completeAuth()"
            (onError)="handleError($event)">
          </app-profile-setup-step>
        }
        @case ('complete') {
          <app-auth-complete
            [userInfo]="userInfo()"
            (onContinue)="navigateToDestination()">
          </app-auth-complete>
        }
      }

      <!-- 进度指示器 -->
      <div class="auth-progress">
        <nz-steps [nzCurrent]="currentStepIndex()" nzSize="small">
          <nz-step nzTitle="登录" nzDescription="输入凭据"></nz-step>
          <nz-step nzTitle="验证" nzDescription="多因素认证" *ngIf="requiresMfa()"></nz-step>
          <nz-step nzTitle="设置" nzDescription="完善资料" *ngIf="requiresProfileSetup()"></nz-step>
          <nz-step nzTitle="完成" nzDescription="认证成功"></nz-step>
        </nz-steps>
      </div>
    </div>
  `
})
export class AuthFlowComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly loadingService = inject(LoadingService);

  // 状态管理
  private readonly currentStepSignal = signal<'login' | 'mfa' | 'profile-setup' | 'complete'>('login');
  private readonly tempTokenSignal = signal<string | null>(null);
  private readonly userInfoSignal = signal<UserInfo | null>(null);
  private readonly errorSignal = signal<string | null>(null);

  // 计算属性
  readonly currentStep = this.currentStepSignal.asReadonly();
  readonly tempToken = this.tempTokenSignal.asReadonly();
  readonly userInfo = this.userInfoSignal.asReadonly();
  readonly error = this.errorSignal.asReadonly();

  readonly currentStepIndex = computed(() => {
    const steps = ['login', 'mfa', 'profile-setup', 'complete'];
    return steps.indexOf(this.currentStep());
  });

  readonly requiresMfa = computed(() => {
    const user = this.userInfo();
    return user?.mfaEnabled ?? false;
  });

  readonly requiresProfileSetup = computed(() => {
    const user = this.userInfo();
    return !user?.profileComplete ?? false;
  });

  ngOnInit(): void {
    // 检查是否已经登录
    if (this.authService.isAuthenticated()) {
      this.navigateToDestination();
    }
  }

  handleLoginSuccess(result: { userInfo: UserInfo; requiresMfa: boolean; tempToken?: string }): void {
    this.userInfoSignal.set(result.userInfo);

    if (result.requiresMfa && result.tempToken) {
      this.tempTokenSignal.set(result.tempToken);
      this.goToStep('mfa');
    } else if (!result.userInfo.profileComplete) {
      this.goToStep('profile-setup');
    } else {
      this.completeAuth();
    }
  }

  handleMfaSuccess(userInfo: UserInfo): void {
    this.userInfoSignal.set(userInfo);
    this.tempTokenSignal.set(null);

    if (!userInfo.profileComplete) {
      this.goToStep('profile-setup');
    } else {
      this.completeAuth();
    }
  }

  handleProfileSetupSuccess(userInfo: UserInfo): void {
    this.userInfoSignal.set(userInfo);
    this.completeAuth();
  }

  completeAuth(): void {
    this.goToStep('complete');

    // 延迟导航，让用户看到成功页面
    setTimeout(() => {
      this.navigateToDestination();
    }, 2000);
  }

  goToStep(step: 'login' | 'mfa' | 'profile-setup' | 'complete'): void {
    this.currentStepSignal.set(step);
    this.errorSignal.set(null);
  }

  handleError(error: string): void {
    this.errorSignal.set(error);
    this.loadingService.clearAll(); // 清除所有加载状态
  }

  navigateToDestination(): void {
    const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    this.router.navigate([returnUrl]);
  }
}
```

## 错误处理示例

### 全局错误处理器

```typescript
// global-error-handler.service.ts
@Injectable({
  providedIn: 'root'
})
export class GlobalErrorHandler implements ErrorHandler {
  private readonly loadingService = inject(LoadingService);
  private readonly message = inject(NzMessageService);
  private readonly router = inject(Router);

  handleError(error: any): void {
    console.error('全局错误:', error);

    // 清除所有加载状态
    this.loadingService.clearAll();

    // 根据错误类型进行处理
    if (error instanceof HttpErrorResponse) {
      this.handleHttpError(error);
    } else if (error instanceof TypeError) {
      this.handleTypeError(error);
    } else if (error instanceof ReferenceError) {
      this.handleReferenceError(error);
    } else {
      this.handleGenericError(error);
    }
  }

  private handleHttpError(error: HttpErrorResponse): void {
    switch (error.status) {
      case 401:
        this.message.error('登录已过期，请重新登录');
        this.router.navigate(['/auth/login']);
        break;
      case 403:
        this.message.error('权限不足，无法访问该资源');
        this.router.navigate(['/error/403']);
        break;
      case 404:
        this.message.error('请求的资源不存在');
        break;
      case 500:
        this.message.error('服务器内部错误，请稍后重试');
        break;
      case 0:
        this.message.error('网络连接失败，请检查网络设置');
        break;
      default:
        this.message.error(`请求失败: ${error.message}`);
    }
  }

  private handleTypeError(error: TypeError): void {
    this.message.error('数据类型错误，请刷新页面重试');
  }

  private handleReferenceError(error: ReferenceError): void {
    this.message.error('引用错误，请刷新页面重试');
  }

  private handleGenericError(error: any): void {
    const message = error.message || '发生未知错误';
    this.message.error(message);
  }
}
```

## 性能优化示例

### 虚拟滚动与懒加载结合

```typescript
// virtual-lazy-list.component.ts
@Component({
  selector: 'app-virtual-lazy-list',
  template: `
    <div class="virtual-lazy-list">
      <cdk-virtual-scroll-viewport
        itemSize="60"
        class="viewport"
        (scrolledIndexChange)="onScrolledIndexChange($event)">

        <div *cdkVirtualFor="let item of visibleItems(); trackBy: trackByFn"
             class="list-item">
          @if (isItemLoaded(item.id)) {
            <div class="item-content">
              <h4>{{ item.title }}</h4>
              <p>{{ item.description }}</p>
            </div>
          } @else {
            <div class="item-skeleton">
              <nz-skeleton [nzActive]="true" [nzParagraph]="{ rows: 2 }"></nz-skeleton>
            </div>
          }
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
  `,
  styles: [`
    .viewport {
      height: 400px;
      width: 100%;
    }

    .list-item {
      height: 60px;
      padding: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .item-content h4 {
      margin: 0 0 4px 0;
      font-size: 14px;
    }

    .item-content p {
      margin: 0;
      font-size: 12px;
      color: #666;
    }
  `]
})
export class VirtualLazyListComponent implements OnInit {
  private readonly loadingService = inject(LoadingService);
  private readonly dataService = inject(DataService);

  // 数据管理
  private readonly allItemsSignal = signal<ListItem[]>([]);
  private readonly loadedItemsSignal = signal<Set<string>>(new Set());
  private readonly visibleRangeSignal = signal<{ start: number; end: number }>({ start: 0, end: 20 });

  // 计算属性
  readonly allItems = this.allItemsSignal.asReadonly();
  readonly loadedItems = this.loadedItemsSignal.asReadonly();
  readonly visibleRange = this.visibleRangeSignal.asReadonly();

  readonly visibleItems = computed(() => {
    const items = this.allItems();
    const range = this.visibleRange();
    return items.slice(range.start, range.end);
  });

  // 配置
  private readonly batchSize = 20;
  private readonly preloadBuffer = 5;

  ngOnInit(): void {
    this.loadInitialData();
  }

  onScrolledIndexChange(index: number): void {
    const newStart = Math.max(0, index - this.preloadBuffer);
    const newEnd = Math.min(this.allItems().length, index + this.batchSize + this.preloadBuffer);

    this.visibleRangeSignal.set({ start: newStart, end: newEnd });

    // 预加载可见范围内的数据
    this.preloadVisibleItems();
  }

  isItemLoaded(itemId: string): boolean {
    return this.loadedItems().has(itemId);
  }

  trackByFn(index: number, item: ListItem): string {
    return item.id;
  }

  private loadInitialData(): void {
    this.loadingService.startLoading('load-list', '加载列表数据...');

    this.dataService.getListMetadata().subscribe({
      next: (metadata) => {
        // 创建占位符项目
        const placeholderItems = Array.from({ length: metadata.totalCount }, (_, index) => ({
          id: `item-${index}`,
          title: '',
          description: '',
          loaded: false
        }));

        this.allItemsSignal.set(placeholderItems);
        this.loadingService.stopLoading('load-list');

        // 加载初始可见项目
        this.preloadVisibleItems();
      },
      error: (error) => {
        console.error('加载列表元数据失败:', error);
        this.loadingService.stopLoading('load-list');
      }
    });
  }

  private preloadVisibleItems(): void {
    const visibleItems = this.visibleItems();
    const unloadedItems = visibleItems.filter(item => !this.isItemLoaded(item.id));

    if (unloadedItems.length === 0) {
      return;
    }

    const itemIds = unloadedItems.map(item => item.id);
    const loadingKey = `load-items-${Date.now()}`;

    this.loadingService.startLoading(loadingKey, `加载 ${itemIds.length} 个项目...`);

    this.dataService.getItemsByIds(itemIds).subscribe({
      next: (items) => {
        // 更新已加载项目集合
        const newLoadedItems = new Set(this.loadedItems());
        items.forEach(item => newLoadedItems.add(item.id));
        this.loadedItemsSignal.set(newLoadedItems);

        // 更新项目数据
        const allItems = [...this.allItems()];
        items.forEach(item => {
          const index = allItems.findIndex(i => i.id === item.id);
          if (index !== -1) {
            allItems[index] = { ...item, loaded: true };
          }
        });
        this.allItemsSignal.set(allItems);

        this.loadingService.stopLoading(loadingKey);
      },
      error: (error) => {
        console.error('加载项目数据失败:', error);
        this.loadingService.stopLoading(loadingKey);
      }
    });
  }
}
```

---

## 总结

本文档提供了 Angular 项目中各种实际场景的完整示例，涵盖了：

1. **企业级页面开发**: 完整的用户管理页面，展示了认证、权限、加载状态的集成
2. **复杂路由配置**: 多层嵌套路由和动态路由注册
3. **守卫组合使用**: 复合守卫实现多重验证逻辑
4. **智能加载管理**: 优化用户体验的加载状态管理
5. **完整认证流程**: 多步骤认证流程的实现
6. **错误处理机制**: 全局错误处理和用户友好的错误提示
7. **性能优化技术**: 虚拟滚动和懒加载的结合使用

这些示例展示了如何在实际项目中正确使用各种功能，遵循最佳实践，构建高质量的 Angular 应用。开发者可以根据具体需求调整和扩展这些示例。
