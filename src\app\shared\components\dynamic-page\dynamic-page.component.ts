import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzTagModule } from 'ng-zorro-antd/tag';

/**
 * 通用动态页面组件
 * 
 * 当无法找到具体的组件时，使用此通用组件作为默认显示
 * 可以根据路由配置动态显示页面内容
 */
@Component({
  selector: 'app-dynamic-page',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzResultModule,
    NzTagModule
  ],
  template: `
    <div class="dynamic-page-container">
      <nz-result
        nzStatus="info"
        [nzTitle]="pageTitle"
        [nzSubTitle]="pageSubtitle">
        
        <div nz-result-content>
          <nz-card nzTitle="页面信息" class="page-info-card">
            <div class="info-grid">
              <div class="info-item">
                <label>路由路径:</label>
                <code>{{ routePath }}</code>
              </div>
              <div class="info-item">
                <label>组件名称:</label>
                <code>{{ componentName || '未指定' }}</code>
              </div>
              <div class="info-item">
                <label>页面标题:</label>
                <span>{{ routeTitle || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>布局类型:</label>
                <nz-tag nzColor="blue">{{ layoutType || 'default' }}</nz-tag>
              </div>
              <div class="info-item">
                <label>权限要求:</label>
                <div class="permissions">
                  @if (permissions && permissions.length > 0) {
                    @for (permission of permissions; track permission) {
                      <nz-tag nzColor="green" class="permission-tag">{{ permission }}</nz-tag>
                    }
                  } @else {
                    <span class="no-data">无特殊权限要求</span>
                  }
                </div>
              </div>
              <div class="info-item">
                <label>角色要求:</label>
                <div class="roles">
                  @if (roles && roles.length > 0) {
                    @for (role of roles; track role) {
                      <nz-tag nzColor="orange" class="role-tag">{{ role }}</nz-tag>
                    }
                  } @else {
                    <span class="no-data">所有角色可访问</span>
                  }
                </div>
              </div>
            </div>
          </nz-card>

          <nz-card nzTitle="开发说明" class="dev-info-card">
            <div class="dev-content">
              <h4>🎯 这是一个通用动态页面组件</h4>
              <p>当系统无法找到指定的组件时，会显示此默认页面。</p>
              
              <h4>🔧 如何添加具体组件？</h4>
              <ol>
                <li>在 <code>src/app/pages/dynamic/</code> 目录下创建对应的组件文件</li>
                <li>按照约定式路径命名：<code>{{ suggestedPath }}</code></li>
                <li>或者在 <code>component-mapping.ts</code> 中添加组件映射</li>
                <li>重新加载路由即可看到具体组件</li>
              </ol>

              <h4>📁 建议的文件结构</h4>
              <pre class="code-block">{{ suggestedStructure }}</pre>
            </div>
          </nz-card>
        </div>

        <div nz-result-extra>
          <button nz-button nzType="primary" (click)="goBack()">
            <nz-icon nzType="arrow-left"></nz-icon>
            返回上一页
          </button>
          <button nz-button (click)="reloadPage()">
            <nz-icon nzType="reload"></nz-icon>
            刷新页面
          </button>
        </div>
      </nz-result>
    </div>
  `,
  styles: [`
    .dynamic-page-container {
      padding: 24px;
      min-height: 400px;
    }

    .page-info-card,
    .dev-info-card {
      margin-bottom: 16px;
      text-align: left;
    }

    .info-grid {
      display: grid;
      gap: 12px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    .info-item label {
      min-width: 80px;
      font-weight: 600;
      color: #262626;
      flex-shrink: 0;
    }

    .info-item code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }

    .permissions,
    .roles {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .permission-tag,
    .role-tag {
      margin: 0;
    }

    .no-data {
      color: #8c8c8c;
      font-style: italic;
    }

    .dev-content h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .dev-content p {
      color: #595959;
      margin: 8px 0;
    }

    .dev-content ol {
      margin: 8px 0 16px 20px;
      color: #595959;
    }

    .dev-content li {
      margin: 4px 0;
    }

    .code-block {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 12px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      white-space: pre;
    }
  `]
})
export class DynamicPageComponent {
  private readonly route = inject(ActivatedRoute);

  // 从路由数据中获取信息
  get routeData() {
    return this.route.snapshot.data;
  }

  get routePath() {
    return this.route.snapshot.routeConfig?.path || '';
  }

  get pageTitle() {
    return this.routeData['title'] || '动态页面';
  }

  get pageSubtitle() {
    return `这是一个动态生成的页面，路径为 /${this.routePath}`;
  }

  get componentName() {
    return this.routeData['componentName'] || this.routeData['component'];
  }

  get routeTitle() {
    return this.routeData['title'];
  }

  get layoutType() {
    return this.routeData['layout'];
  }

  get permissions() {
    return this.routeData['permissions'] || [];
  }

  get roles() {
    return this.routeData['roles'] || [];
  }

  get suggestedPath() {
    const path = this.routePath;
    const componentName = this.componentName || `${this.kebabCase(path)}.component`;
    return `src/app/pages/dynamic/${path}/${componentName}.ts`;
  }

  get suggestedStructure() {
    const path = this.routePath;
    const componentName = this.componentName || `${this.pascalCase(path)}Component`;
    
    return `src/app/pages/dynamic/${path}/
├── ${this.kebabCase(path)}.component.ts
├── ${this.kebabCase(path)}.component.html
├── ${this.kebabCase(path)}.component.scss
└── index.ts

// ${this.kebabCase(path)}.component.ts
@Component({
  selector: 'app-${this.kebabCase(path)}',
  standalone: true,
  templateUrl: './${this.kebabCase(path)}.component.html',
  styleUrls: ['./${this.kebabCase(path)}.component.scss']
})
export class ${componentName} {
  // 组件实现
}`;
  }

  /**
   * 返回上一页
   */
  goBack(): void {
    window.history.back();
  }

  /**
   * 刷新页面
   */
  reloadPage(): void {
    window.location.reload();
  }

  /**
   * 将字符串转换为短横线命名
   */
  private kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  /**
   * 将字符串转换为帕斯卡命名
   */
  private pascalCase(str: string): string {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }
}
