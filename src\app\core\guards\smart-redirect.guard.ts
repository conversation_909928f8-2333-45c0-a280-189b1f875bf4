import { Injectable, inject } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { AuthService } from '../services/auth.service';
import { DEFAULT_ROUTES } from '../constants';

/**
 * 智能重定向守卫
 *
 * 根据用户的认证状态和角色进行智能重定向，主要用于处理根路径（"/"）的访问。
 * 确保已认证用户能够直接访问其对应角色的默认页面，而未认证用户重定向到登录页。
 *
 * @description
 * 重定向逻辑：
 * - 未认证用户：重定向到登录页面（/auth/login）
 * - 已认证的管理员：重定向到管理员仪表盘（/admin/dashboard）
 * - 已认证的普通用户：重定向到用户仪表盘（/user/dashboard）
 * - 认证状态检查失败：重定向到登录页面
 *
 * 设计特点：
 * - 支持快速本地状态检查和深度服务端验证
 * - 基于用户角色的智能重定向
 * - 完善的错误处理机制
 * - 与现有认证系统无缝集成
 *
 * @example
 * ```typescript
 * // 在路由配置中使用
 * {
 *   path: '',
 *   canActivate: [SmartRedirectGuard],
 *   children: []
 * }
 * ```
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Injectable({
  providedIn: 'root'
})
export class SmartRedirectGuard implements CanActivate {
  /** 认证服务实例，用于检查用户认证状态和获取用户信息 */
  private readonly authService = inject(AuthService);

  /** 路由服务实例，用于执行重定向操作 */
  private readonly router = inject(Router);

  /**
   * 守卫激活检查
   *
   * 检查用户认证状态并执行相应的重定向操作。
   * 此方法总是返回 false，因为它的目的是执行重定向而不是允许访问当前路由。
   *
   * @returns Observable<boolean> - 总是返回 false，因为会执行重定向
   *
   * @description
   * 执行流程：
   * 1. 快速检查本地认证状态
   * 2. 如果已认证，根据用户角色重定向到对应页面
   * 3. 如果未认证，进行深度验证
   * 4. 根据验证结果执行相应的重定向
   */
  canActivate(): Observable<boolean> {
    // 快速检查：如果用户已经认证（本地状态检查）
    if (this.authService.isAuthenticated()) {
      // 根据用户角色进行重定向
      this.redirectBasedOnUserRole();
      return of(false); // 返回 false 因为我们执行了重定向
    }

    // 深度验证：调用服务端验证认证状态
    return this.authService.checkAuthStatus().pipe(
      map(isAuthenticated => {
        if (isAuthenticated) {
          // 认证有效，根据用户角色重定向
          this.redirectBasedOnUserRole();
        } else {
          // 认证无效，重定向到登录页
          this.router.navigate([DEFAULT_ROUTES.LOGIN]);
        }
        return false; // 总是返回 false 因为我们执行了重定向
      }),
      catchError(() => {
        // 认证检查过程中发生错误，重定向到登录页
        this.router.navigate([DEFAULT_ROUTES.LOGIN]);
        return of(false);
      })
    );
  }

  /**
   * 根据用户角色执行重定向
   *
   * 获取当前用户信息，根据用户的角色重定向到相应的默认页面。
   *
   * @private
   * @returns void
   *
   * @description
   * 重定向规则：
   * - 管理员角色：重定向到 /admin/dashboard
   * - 普通用户角色：重定向到 /user/dashboard
   * - 无角色或未知角色：重定向到登录页面
   *
   * 如果用户拥有多个角色，优先级为：admin > user
   */
  private redirectBasedOnUserRole(): void {
    console.log('根据用户角色进行重定向');
    const userInfo = this.authService.userInfo();

    if (!userInfo || !userInfo.roles || userInfo.roles.length === 0) {
      // 用户信息不完整，重定向到登录页
      console.warn('用户信息不完整，重定向到登录页');
      this.router.navigate([DEFAULT_ROUTES.LOGIN]);
      return;
    }

    // 检查用户角色并重定向
    const roles = userInfo.roles.map(role => role.name);

    if (roles.includes('admin')) {
      // 管理员用户重定向到管理员仪表盘
      console.log('检测到管理员角色，重定向到管理员仪表盘');
      this.router.navigate(['/view/dashboard']);
    } else if (roles.includes('user')) {
      // 普通用户重定向到用户仪表盘
      console.log('检测到用户角色，重定向到用户仪表盘');
      this.router.navigate(['/view/dashboard']);
    } else {
      // 未知角色，重定向到登录页
      console.warn('未知用户角色:', roles, '重定向到登录页');
      this.router.navigate([DEFAULT_ROUTES.LOGIN]);
    }
  }
}
